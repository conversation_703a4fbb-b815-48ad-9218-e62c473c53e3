# Refactor 2.0 Journey - Cesta k pravde

## 🌟 Ako sme padli a vstali

> "<PERSON>rv<PERSON> je pravda. Druh<PERSON> je len spôsob jej použitia."

### 📅 Začiatok - Rozpoznanie pravdy

**Problém**: Architektúra bola dobrá, ale z<PERSON>šaná s implementáciami.
- <PERSON><PERSON><PERSON> existovali, ale neboli **čisté**
- DI registrácia roztrúsená v `config/container.php`
- Auth logika zmiešaná s User modulom
- Testovanie nebolo **izolované**

**Rozpoznanie**: Potrebujeme vrátiť sa k **abstraktným základom** - k pravde pred implementáciou.

### 🎯 Prvý krok - ModuleInterface (pravda o module)

```php
interface ModuleInterface
{
    // Kto som?
    public function getName(): string;

    // Na kom závisím?
    public function getDependencies(): array;

    // Ako sa registrujem?
    public function register(RegistryInterface $registry): void;
}
```

**Duchovný princíp**: Každý modul musí vedieť svoju **identitu**, **závislosti** a **spôsob prejavenia**.

### 🔧 Druhý krok - RegistryInterface (pravda o registrácii)

```php
interface RegistryInterface
{
    public function set(string $identity, mixed $definition): void;
    public function bind(string $interface, string $implementation): void;
    public function has(string $identity): bool;
    public function get(string $identity): mixed;
}
```

**Duchovný princíp**: Registrácia je **most** medzi pravdou (interface) a realitou (implementácia).

### 🧪 Tretí krok - MockRegistry (falošná realita pre test pravdy)

```php
final class MockRegistry implements RegistryInterface
{
    // Predstiera DI kontajner
    // Umožňuje testovať pravdu bez skutočnej reality
}
```

**Duchovný princíp**: Pravda musí byť testovateľná **v izolácii** - bez závislostí na vonkajšom svete.

## 👤 UserModule - Svedok identity

### 🎯 Čo sme vytvorili:

✅ **UserServiceInterface** - pravda o User službách
✅ **UserService** - orchestrácia User operácií
✅ **UserModule** - registrácia bez framework závislostí
✅ **UserModuleTest** - 8 testov pravdy ✅

### 💎 Duchovné princípy:

```php
final class UserModule implements ModuleInterface
{
    public function getName(): string { return 'user'; }
    public function getDependencies(): array { return []; } // nezávislý
    public function register(RegistryInterface $registry): void {
        // Registruje len rozhrania, nie implementácie
    }
}
```

**Pravda**: User modul vie **kto je kto**, nezávisle od spôsobu uloženia.

### 🧪 Test pravdy:

```php
public function testModuleRegistersBasicServices(): void
{
    $this->userModule->register($this->mockRegistry);

    $this->assertTrue($this->mockRegistry->has(UserServiceInterface::class));
    // Pravda je overená - modul vie zaregistrovať svoju podstatu
}
```

## 🔐 AuthModule - Strážca overenia

### 🎯 Čo sme vytvorili:

✅ **AuthToken** - pravda o overení (kto som, kedy, aké oprávnenia)
✅ **Permission** - pravda o oprávnení (čo môže byť vykonané)
✅ **AuthServiceInterface** - abstraktná pravda o overení
✅ **SessionAuth** - spôsob overenia cez session
✅ **NullAuth** - spôsob pre testovanie
✅ **AuthModule** - registrácia s User závislosťou
✅ **AuthModuleTest** - 11 testov pravdy ✅

### 💎 Duchovné princípy:

```php
interface AuthServiceInterface
{
    // Kto si?
    public function authenticate(string $identity, string $credential): ?AuthToken;

    // Môžeš?
    public function authorize(AuthToken $token, Permission $permission): bool;

    // Odíď.
    public function logout(AuthToken $token): void;
}
```

**Pravda**: Auth modul vie **kto môže čo**, závisí na User module (kto je kto).

### 🔗 Vzťah modulov:

```
Root (Otec)
├── User (identita) - "Kto je kto?"
└── Auth (overenie) - "Kto môže čo?"
    └── závisí na User
```

## 🌊 Tok závislostí - Smer pravdy

```
Infrastructure → Application → Domain
```

- **Domain** = čistá pravda (bez závislostí)
- **Application** = orchestrácia pravdy
- **Infrastructure** = spôsob prejavenia pravdy

### Inverzia závislostí:

```php
// Pravda (Domain)
interface UserRepositoryInterface { }

// Spôsob (Infrastructure)
class SqliteUserRepository implements UserRepositoryInterface { }

// Orchestrácia (Application)
class UserService
{
    public function __construct(
        private UserRepositoryInterface $repository // závisí na pravde, nie na spôsobe
    ) {}
}
```

## 🧪 Testovanie pravdy

### Princíp izolácie:

Každý modul testujeme **samostatne** s **mock** implementáciami.
Pravda modulu nesmie závisieť od iných modulov.

```php
public function testUserCanBeCreated(): void
{
    // Arrange - priprav mock registry
    $mockRegistry = new MockRegistry();

    // Act - zaregistruj modul
    $userModule = new UserModule();
    $userModule->register($mockRegistry);

    // Assert - over pravdu
    $this->assertTrue($mockRegistry->has(UserServiceInterface::class));
}
```

### Mock implementácie - falošná realita pre test pravdy:

- `MockRegistry` - predstiera DI kontajner
- `NullAuth` - predstiera overenie
- `MockUserRepository` - predstiera uloženie

## 🏷️ MarkModule - Svedok označenia

### 🎯 Čo sme vytvorili:

✅ **MarkServiceInterface** - pravda o Mark službách
✅ **MarkService** - orchestrácia označení
✅ **MarkModule** - registrácia s User závislosťou
✅ **MarkModuleTest** - 12 testov pravdy ✅
✅ **DbMarkRepository** - opravená `count()` metóda

### 💎 Duchovné princípy:

```php
final class MarkModule implements ModuleInterface
{
    public function getName(): string { return 'mark'; }
    public function getDependencies(): array { return ['user']; } // závisí na User
    public function register(RegistryInterface $registry): void {
        // Registruje služby pre označenia
    }
}
```

**Pravda**: Mark modul vie **čo je označené**, závisí na User module (kto označuje).

### 🔗 Vzťah modulov:

```
Root (Otec)
├── User (identita) - "Kto je kto?"
├── Auth (overenie) - "Kto môže čo?"
│   └── závisí na User
└── Mark (označenie) - "Čo je označené?"
    └── závisí na User
```

## 📊 Výsledky cesty

### ✅ Úspechy:

- **31 testov pravdy prešlo** (8 User + 11 Auth + 12 Mark)
- **Čisté rozhrania** bez framework závislostí
- **Izolované testovanie** modulov
- **Jasné zodpovednosti** - User (identita), Auth (overenie), Mark (označenie)
- **Správny tok závislostí** - dovnútra k Domain

### 🎯 Dosiahnuté princípy:

1. **Pravda je jedna** - ModuleInterface, AuthServiceInterface
2. **Spôsoby sú mnohé** - SessionAuth, NullAuth, MockRegistry
3. **Moduly sú nezávislé** - User nezávisí na nikomu, Auth závisí len na User
4. **Testovateľnosť** - každý modul testovateľný v izolácii
5. **Čistota** - business logika oddelená od infraštruktúry

## 💡 Filozofia cesty

> "Pravda je jedna, spôsoby jej prejavenia sú mnohé."

- **ModuleInterface** = pravda o module
- **Slim 4** = spôsob prejavenia web aplikácie
- **SQLite** = spôsob prejavenia uloženia
- **PHPUnit** = spôsob prejavenia testovania

**Refactor 2.0** = návrat k **pravde** (abstrakciám) pred **spôsobmi** (implementáciami).

## 🚀 Ďalšie kroky

### Pripravené na:

1. **MarkModule** - pravda o označení
2. **EventBus** - pravda o reakcii na zmenu
3. **CoreModule** - základné služby bez framework závislostí

### Commit messages cesty:

```
feat(core): ModuleInterface - pravda o module
feat(core): RegistryInterface + MockRegistry - test pravdy
feat(user): UserModule - svedok identity nezávislý od spôsobov
feat(auth): AuthModule - strážca overenia závislý len na identite
feat(mark): MarkModule - svedok označenia závislý len na identite
```

## 🌟 Pre ťažké časy - Inšpirácia

### 💪 Čo sme už dokázali:

**31 testov pravdy** - to nie je len číslo. To je **31 dôkazov**, že pravda existuje a dá sa overiť.

**3 čisté moduly** - User, Auth, Mark. Každý vie svoju zodpovednosť. Každý je testovateľný v izolácii. Každý je **svedkom pravdy**.

**Žiadne framework závislosti** - naša business logika je **čistá**. Môžeme zmeniť Slim na Symfony, SQLite na PostgreSQL, PHPUnit na Pest - a pravda zostane.

### 🔥 Keď príde depresia:

**Spomeň si**: Začali sme s chaotickým `config/container.php` a skončili s **čistými modulmi**.

**Spomeň si**: Každý test, ktorý prejde, je **víťazstvo pravdy** nad chaosom.

**Spomeň si**: Nie sme len programátori. Sme **architekti pravdy**.

### 🎯 Prečo to robíme:

Nie pre framework. Nie pre technológie.
**Pre pravdu**. Pre to, aby kód bol **živý**, nie mŕtvy.
Pre to, aby budúci pomocníci vedeli, **čo sme mysleli**.

## 🚀 Čo máme ešte pred sebou

### 🔄 EventBus - Pravda o komunikácii

**Vízia**: Moduly si budú posielať správy bez toho, aby o sebe vedeli.

```php
interface EventBusInterface
{
    // Pošli správu
    public function dispatch(DomainEvent $event): void;

    // Počúvaj správy
    public function subscribe(string $eventType, callable $handler): void;
}
```

**Príklad**: Keď sa vytvorí User, Auth modul sa dozvie a vytvorí prázdne oprávnenia.

### 🏗️ CoreModule - Pravda o základoch

**Vízia**: Služby, ktoré potrebuje každý modul, ale nezávisia na žiadnom.

```php
final class CoreModule implements ModuleInterface
{
    public function getName(): string { return 'core'; }
    public function getDependencies(): array { return []; } // nezávislý
    public function register(RegistryInterface $registry): void {
        // Logger, Settings, EventBus, Clock...
    }
}
```

### 📰 ArticleModule - Pravda o obsahu

**Vízia**: Články ako nezávislé entity s vlastnou logikou.

```php
interface ArticleServiceInterface
{
    // Čo je napísané?
    public function createArticle(UserId $authorId, string $title, string $content): ArticleId;

    // Kto to napísal?
    public function findArticlesByAuthor(UserId $authorId): array;

    // Je to publikované?
    public function publishArticle(ArticleId $id): bool;
}
```

### 🔐 PermissionModule - Pravda o oprávneniach

**Vízia**: Sofistikovaný systém oprávnení nezávislý od Auth.

```php
interface PermissionServiceInterface
{
    // Môže používateľ vykonať akciu na zdroji?
    public function can(UserId $userId, string $action, string $resource): bool;

    // Pridaj oprávnenie
    public function grant(UserId $userId, Permission $permission): void;

    // Odobier oprávnenie
    public function revoke(UserId $userId, Permission $permission): void;
}
```

### 🌐 WebModule - Pravda o HTTP

**Vízia**: HTTP rozhranie ako samostatný modul, nezávislý od business logiky.

```php
final class WebModule implements ModuleInterface
{
    public function getDependencies(): array {
        return ['user', 'auth', 'mark', 'article']; // používa všetky
    }
    public function register(RegistryInterface $registry): void {
        // Routes, Controllers, Middleware, Views...
    }
}
```

### 🧪 TestModule - Pravda o testovaní

**Vízia**: Testovanie ako modul s vlastnými nástrojmi.

```php
final class TestModule implements ModuleInterface
{
    public function register(RegistryInterface $registry): void {
        // TestFixtures, MockFactories, TestDatabase...
    }
}
```

## 🎭 Finálna vízia - Kompletná architektúra

```
Root (Otec)
├── Core (základy) - Logger, EventBus, Settings
├── User (identita) - "Kto je kto?"
├── Auth (overenie) - "Kto môže čo?"
├── Permission (oprávnenia) - "Čo je dovolené?"
├── Mark (označenie) - "Čo je označené?"
├── Article (obsah) - "Čo je napísané?"
├── Web (HTTP) - "Ako sa to zobrazí?"
└── Test (testovanie) - "Ako sa to overí?"
```

### 🏆 Konečný cieľ:

**100+ testov pravdy** - každý modul plne pokrytý
**Framework agnostic** - možnosť zmeny bez straty business logiky
**Event-driven** - moduly komunikujú cez udalosti
**Čistá architektúra** - závislosti smerujú dovnútra
**Živý kód** - každý riadok má zmysel a účel

## 💫 Záver pre ťažké časy

**Keď zabudneš prečo**: Pozri sa na testy. **31 zelených** znamená **31 víťazstiev**.

**Keď stratíš smer**: Pozri sa na ModuleInterface. **Každý modul vie kto je**.

**Keď príde chaos**: Pozri sa na závislosti. **Všetko smeruje k pravde**.

**Keď pochybuješ**: Spomeň si - **začali sme s ničím a vytvorili sme pravdu**.

---

**Ak padneme, vstaneme. Ak vstaneme, zapíšeme. Ak zapíšeme, inšpirujeme.**
**Lebo pravda musí byť zachovaná - pre nás a pre tých, čo prídu po nás.** 🙏✨
