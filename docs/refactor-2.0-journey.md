# Refactor 2.0 Journey - Cesta k pravde

## 🌟 Ako sme padli a vstali

> "<PERSON>rv<PERSON> je pravda. Druh<PERSON> je len spôsob jej použitia."

### 📅 Začiatok - Rozpoznanie pravdy

**Problém**: Architektúra bola dobrá, ale z<PERSON>šaná s implementáciami.
- <PERSON><PERSON><PERSON> existovali, ale neboli **čisté**
- DI registrácia roztrúsená v `config/container.php`
- Auth logika zmiešaná s User modulom
- Testovanie nebolo **izolované**

**Rozpoznanie**: Potrebujeme vrátiť sa k **abstraktným základom** - k pravde pred implementáciou.

### 🎯 Prvý krok - ModuleInterface (pravda o module)

```php
interface ModuleInterface 
{
    // Kto som?
    public function getName(): string;
    
    // Na kom závisím?
    public function getDependencies(): array;
    
    // Ako sa registrujem?
    public function register(RegistryInterface $registry): void;
}
```

**Duchovný princíp**: Každý modul musí vedieť svoju **identitu**, **závislosti** a **spôsob prejavenia**.

### 🔧 Druhý krok - RegistryInterface (pravda o registrácii)

```php
interface RegistryInterface 
{
    public function set(string $identity, mixed $definition): void;
    public function bind(string $interface, string $implementation): void;
    public function has(string $identity): bool;
    public function get(string $identity): mixed;
}
```

**Duchovný princíp**: Registrácia je **most** medzi pravdou (interface) a realitou (implementácia).

### 🧪 Tretí krok - MockRegistry (falošná realita pre test pravdy)

```php
final class MockRegistry implements RegistryInterface
{
    // Predstiera DI kontajner
    // Umožňuje testovať pravdu bez skutočnej reality
}
```

**Duchovný princíp**: Pravda musí byť testovateľná **v izolácii** - bez závislostí na vonkajšom svete.

## 👤 UserModule - Svedok identity

### 🎯 Čo sme vytvorili:

✅ **UserServiceInterface** - pravda o User službách  
✅ **UserService** - orchestrácia User operácií  
✅ **UserModule** - registrácia bez framework závislostí  
✅ **UserModuleTest** - 8 testov pravdy ✅  

### 💎 Duchovné princípy:

```php
final class UserModule implements ModuleInterface
{
    public function getName(): string { return 'user'; }
    public function getDependencies(): array { return []; } // nezávislý
    public function register(RegistryInterface $registry): void {
        // Registruje len rozhrania, nie implementácie
    }
}
```

**Pravda**: User modul vie **kto je kto**, nezávisle od spôsobu uloženia.

### 🧪 Test pravdy:

```php
public function testModuleRegistersBasicServices(): void
{
    $this->userModule->register($this->mockRegistry);
    
    $this->assertTrue($this->mockRegistry->has(UserServiceInterface::class));
    // Pravda je overená - modul vie zaregistrovať svoju podstatu
}
```

## 🔐 AuthModule - Strážca overenia

### 🎯 Čo sme vytvorili:

✅ **AuthToken** - pravda o overení (kto som, kedy, aké oprávnenia)  
✅ **Permission** - pravda o oprávnení (čo môže byť vykonané)  
✅ **AuthServiceInterface** - abstraktná pravda o overení  
✅ **SessionAuth** - spôsob overenia cez session  
✅ **NullAuth** - spôsob pre testovanie  
✅ **AuthModule** - registrácia s User závislosťou  
✅ **AuthModuleTest** - 11 testov pravdy ✅  

### 💎 Duchovné princípy:

```php
interface AuthServiceInterface 
{
    // Kto si?
    public function authenticate(string $identity, string $credential): ?AuthToken;
    
    // Môžeš?
    public function authorize(AuthToken $token, Permission $permission): bool;
    
    // Odíď.
    public function logout(AuthToken $token): void;
}
```

**Pravda**: Auth modul vie **kto môže čo**, závisí na User module (kto je kto).

### 🔗 Vzťah modulov:

```
Root (Otec)
├── User (identita) - "Kto je kto?"
└── Auth (overenie) - "Kto môže čo?"
    └── závisí na User
```

## 🌊 Tok závislostí - Smer pravdy

```
Infrastructure → Application → Domain
```

- **Domain** = čistá pravda (bez závislostí)
- **Application** = orchestrácia pravdy  
- **Infrastructure** = spôsob prejavenia pravdy

### Inverzia závislostí:

```php
// Pravda (Domain)
interface UserRepositoryInterface { }

// Spôsob (Infrastructure) 
class SqliteUserRepository implements UserRepositoryInterface { }

// Orchestrácia (Application)
class UserService 
{
    public function __construct(
        private UserRepositoryInterface $repository // závisí na pravde, nie na spôsobe
    ) {}
}
```

## 🧪 Testovanie pravdy

### Princíp izolácie:

Každý modul testujeme **samostatne** s **mock** implementáciami.
Pravda modulu nesmie závisieť od iných modulov.

```php
public function testUserCanBeCreated(): void 
{
    // Arrange - priprav mock registry
    $mockRegistry = new MockRegistry();
    
    // Act - zaregistruj modul
    $userModule = new UserModule();
    $userModule->register($mockRegistry);
    
    // Assert - over pravdu
    $this->assertTrue($mockRegistry->has(UserServiceInterface::class));
}
```

### Mock implementácie - falošná realita pre test pravdy:

- `MockRegistry` - predstiera DI kontajner
- `NullAuth` - predstiera overenie
- `MockUserRepository` - predstiera uloženie

## 📊 Výsledky cesty

### ✅ Úspechy:

- **19 testov pravdy prešlo** (8 User + 11 Auth)
- **Čisté rozhrania** bez framework závislostí
- **Izolované testovanie** modulov
- **Jasné zodpovednosti** - User (identita), Auth (overenie)
- **Správny tok závislostí** - dovnútra k Domain

### 🎯 Dosiahnuté princípy:

1. **Pravda je jedna** - ModuleInterface, AuthServiceInterface
2. **Spôsoby sú mnohé** - SessionAuth, NullAuth, MockRegistry
3. **Moduly sú nezávislé** - User nezávisí na nikomu, Auth závisí len na User
4. **Testovateľnosť** - každý modul testovateľný v izolácii
5. **Čistota** - business logika oddelená od infraštruktúry

## 💡 Filozofia cesty

> "Pravda je jedna, spôsoby jej prejavenia sú mnohé."

- **ModuleInterface** = pravda o module
- **Slim 4** = spôsob prejavenia web aplikácie
- **SQLite** = spôsob prejavenia uloženia
- **PHPUnit** = spôsob prejavenia testovania

**Refactor 2.0** = návrat k **pravde** (abstrakciám) pred **spôsobmi** (implementáciami).

## 🚀 Ďalšie kroky

### Pripravené na:

1. **MarkModule** - pravda o označení
2. **EventBus** - pravda o reakcii na zmenu
3. **CoreModule** - základné služby bez framework závislostí

### Commit messages cesty:

```
feat(core): ModuleInterface - pravda o module
feat(core): RegistryInterface + MockRegistry - test pravdy
feat(user): UserModule - svedok identity nezávislý od spôsobov  
feat(auth): AuthModule - strážca overenia závislý len na identite
```

---

**Ak padneme, vstaneme. Ak vstaneme, zapíšeme. Lebo pravda musí byť zachovaná.** 🙏✨
