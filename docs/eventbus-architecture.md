# EventBus Architecture - Pravda o komunikácii

## 🔄 Základná filozofia

> "Moduly sú ako ľudia - potrebujú si vedieť povedať, čo sa stalo, bez toho aby sa navzájom poznali."

**EventBus** je **most medzi modulmi** - umožňuje im komunikovať bez závislostí.

## 🎯 Abstraktné rozhrania (Pravda)

### DomainEvent - podstata udalosti

```php
abstract readonly class DomainEvent
{
    // Aká je moja identita?
    public function getEventId(): string;
    
    // Kedy som sa stala?
    public function getOccurredAt(): \DateTimeImmutable;
    
    // Aké sú moje dáta?
    public function getEventData(): array;
    
    // Aký je môj typ?
    public function getEventType(): string;
}
```

**Princíp**: Ka<PERSON><PERSON><PERSON> udal<PERSON> má **identitu**, **čas** a **dá<PERSON>**. Nezávisí od toho, kto ju počúva.

### EventBusInterface - spôsob komunik<PERSON>cie

```php
interface EventBusInterface
{
    // Pošli správu do sveta
    public function dispatch(DomainEvent $event): void;
    
    // Počúvaj správy určitého typu
    public function subscribe(string $eventType, callable $handler): void;
    
    // Prestaň počúvať
    public function unsubscribe(string $eventType, ?callable $handler = null): void;
    
    // Aké správy počúvam?
    public function getSubscriptions(): array;
}
```

**Princíp**: **Jeden pošle**, **iní počúvajú**. Žiadne priame závislosti.

## 🌟 Konkrétne udalosti

### UserCreated - "Používateľ bol vytvorený"

```php
final readonly class UserCreated extends DomainEvent
{
    public static function occur(UserId $userId, string $email, string $username): self
    {
        return self::create([
            'userId' => (string)$userId,
            'email' => $email,
            'username' => $username,
        ]);
    }
    
    public function getUserId(): UserId;
    public function getEmail(): string;
    public function getUsername(): string;
}
```

**Použitie**: Keď UserModule vytvorí používateľa, pošle túto správu.

### UserLoggedIn - "Používateľ sa prihlásil"

```php
final readonly class UserLoggedIn extends DomainEvent
{
    public static function occur(UserId $userId, string $tokenId, array $permissions = []): self;
    
    public function getUserId(): UserId;
    public function getTokenId(): string;
    public function getPermissions(): array;
}
```

**Použitie**: Keď AuthModule overí používateľa, pošle túto správu.

### MarkCreated - "Označenie bolo vytvorené"

```php
final readonly class MarkCreated extends DomainEvent
{
    public static function occur(MarkId $markId, UserId $userId, string $title, bool $isPublic = false): self;
    
    public function getMarkId(): MarkId;
    public function getUserId(): UserId;
    public function getTitle(): string;
    public function isPublic(): bool;
}
```

**Použitie**: Keď MarkModule vytvorí označenie, pošle túto správu.

## 🛠️ Implementácie

### InMemoryEventBus - synchronná komunikácia

```php
final class InMemoryEventBus implements EventBusInterface
{
    // Udalosti sa spracovávajú okamžite v rovnakom procese
    // Ideálne pre jednoduchú aplikáciu
    
    // Bonus metódy pre testovanie:
    public function getDispatchedEvents(): array;
    public function clearHistory(): void;
    public function getLastEventOfType(string $eventType): ?DomainEvent;
    public function countEventsOfType(string $eventType): int;
}
```

**Použitie**: Pre väčšinu aplikácií, kde nepotrebujeme async spracovanie.

### NullEventBus - testovacia implementácia

```php
final class NullEventBus implements EventBusInterface
{
    // Všetko "pošle" ale nič sa nestane
    // Ideálne pre unit testy
    
    // Bonus metódy pre testovanie:
    public function wasEventDispatched(string $eventType): bool;
    public function countEventsOfType(string $eventType): int;
}
```

**Použitie**: Pre unit testy, kde nechceme skutočné side effects.

## 🔗 Komunikačné vzory

### 1. Notifikácia o vytvorení

```php
// UserModule
$userId = $this->userRepository->save($user);
$this->eventBus->dispatch(UserCreated::occur($userId, $email, $username));

// AuthModule (počúva)
$this->eventBus->subscribe(UserCreated::class, function(UserCreated $event) {
    // Vytvor prázdne oprávnenia pre nového používateľa
    $this->createDefaultPermissions($event->getUserId());
});
```

### 2. Audit log

```php
// Všetky moduly môžu počúvať všetky udalosti
$this->eventBus->subscribe(DomainEvent::class, function(DomainEvent $event) {
    $this->auditLogger->log($event->toString());
});
```

### 3. Cache invalidation

```php
// Keď sa zmení User, invaliduj cache
$this->eventBus->subscribe(UserCreated::class, [$this->cache, 'invalidateUserCache']);
$this->eventBus->subscribe(UserUpdated::class, [$this->cache, 'invalidateUserCache']);
```

## 🧪 Testovanie s EventBus

### Unit testy - izolácia

```php
class UserServiceTest extends TestCase
{
    public function testUserCreationDispatchesEvent(): void
    {
        // Arrange
        $eventBus = new NullEventBus();
        $userService = new UserService($repository, $eventBus);
        
        // Act
        $userId = $userService->createUser($userData);
        
        // Assert
        $this->assertTrue($eventBus->wasEventDispatched(UserCreated::class));
        $this->assertSame(1, $eventBus->countEventsOfType(UserCreated::class));
    }
}
```

### Integration testy - komunikácia

```php
class UserAuthIntegrationTest extends TestCase
{
    public function testUserCreationTriggersAuthSetup(): void
    {
        // Arrange
        $eventBus = new InMemoryEventBus();
        $userService = new UserService($userRepo, $eventBus);
        $authService = new AuthService($authRepo, $eventBus);
        
        // Subscribe auth to user events
        $eventBus->subscribe(UserCreated::class, [$authService, 'handleUserCreated']);
        
        // Act
        $userId = $userService->createUser($userData);
        
        // Assert
        $this->assertTrue($authService->hasDefaultPermissions($userId));
    }
}
```

## 🎭 Výhody EventBus architektúry

### ✅ Čo získavame:

1. **Loose coupling** - moduly sa nepoznajú priamo
2. **Extensibility** - ľahko pridáme nové poslucháče
3. **Testability** - môžeme testovať komunikáciu
4. **Auditability** - vidíme všetky udalosti
5. **Flexibility** - môžeme meniť implementáciu EventBus

### 🔄 Tok udalostí:

```
UserModule → UserCreated → EventBus → [AuthModule, AuditModule, CacheModule]
AuthModule → UserLoggedIn → EventBus → [AuditModule, SessionModule]
MarkModule → MarkCreated → EventBus → [NotificationModule, SearchModule]
```

## 🚀 Budúce rozšírenia

### AsyncEventBus - pre náročné aplikácie

```php
final class AsyncEventBus implements EventBusInterface
{
    // Udalosti sa spracovávajú v queue (Redis, RabbitMQ, ...)
    // Pre high-performance aplikácie
}
```

### PersistentEventBus - event sourcing

```php
final class PersistentEventBus implements EventBusInterface
{
    // Všetky udalosti sa ukladajú do databázy
    // Pre audit trail a event sourcing
}
```

### RetryableEventBus - odolnosť voči chybám

```php
final class RetryableEventBus implements EventBusInterface
{
    // Ak handler zlyhá, skúsi to znovu
    // Pre kritické business procesy
}
```

## 💡 Najlepšie praktiky

### ✅ Robiť:

- **Malé, špecifické udalosti** - UserCreated, nie UserChanged
- **Immutable event data** - readonly properties
- **Descriptive names** - MarkPublished, nie MarkUpdated
- **Include context** - userId, timestamp, relevant IDs

### ❌ Nerobiť:

- **Veľké, generické udalosti** - EntityChanged
- **Mutable event data** - môže sa zmeniť počas spracovania
- **Business logic v eventoch** - len dáta, nie logika
- **Synchronous heavy operations** - môže zablokovať dispatch

## 🌟 Záver

**EventBus** je **nervový systém** našej aplikácie - prenáša správy medzi modulmi bez toho, aby sa navzájom poznali.

Je to **pravda o komunikácii** - jeden pošle, iní počúvajú, všetci sú nezávislí.

**Testovateľné**, **rozšíriteľné**, **čisté**. 🙏✨
