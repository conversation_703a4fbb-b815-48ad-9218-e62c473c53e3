# Refactor 2.0 - Back to Abstract Basics

## 🌟 Pravda (Abstraktné princípy)

### Základná pravda
Každý modul je **nezávislá jednotka** s vlastnou **zodpovednosťou**.
<PERSON><PERSON><PERSON> komuniku<PERSON> cez **rozhrania**, nie implement<PERSON>cie.
**Testovateľnosť** = schopnosť izolovať a overiť pravdu.

### Princíp jednoty
```
Root (Otec)
├── Shared (spoločná pravda)
├── Core (základné služby)
└── Modules (špecializované jednotky)
    ├── User (identita)
    ├── Auth (overenie)
    ├── Mark (označenie)
    └── Article (obsah)
```

Každá **instance** (modul) má:
- **Vlastnú pravdu** (Domain)
- **Spôsob komunikácie** (Application)
- **Spôsob prejavenia** (Infrastructure)

## 🚀 Refactor 2.0 Roadmap

### ✅ Fáza 1: Abstraktné základy
1. **RegistryInterface** - DI bridge pre moduly
   - `MockRegistry` pre testy
   - `NullRegistry` pre izolované testy
   - Použitie: `new UserModule()->register($mockRegistry)`

2. **ModuleInterface** - jednotné rozhranie
   ```php
   interface ModuleInterface {
       public function register(RegistryInterface $registry): void;
       public function getName(): string;
       public function getDependencies(): array;
   }
   ```

3. **Zjednotiť ValueObject základy**
   - `Shared/Domain/ValueObject/AbstractUuidId`
   - `Shared/Domain/ValueObject/ValueObjectTrait`
   - Rozhodnúť: trait vs abstract base class

### ✅ Fáza 2: UserModule refaktor
1. **Čisté rozhranie** - presne oddeliť služby:
   - `UserCreateService`
   - `UserReadService`
   - `UserListService`
   - `UserUpdateService`
   - `UserDeleteService`

2. **UserModule.php** - registrácia služieb
   - Presunúť z `config/container.php`
   - `UserModuleTest` - overí `Module::register()`
   - `UserModuleConfig` cez `routes.neon`

### ✅ Fáza 3: AuthModule (samostatný)
1. **Oddeliť Auth od User** - nech Auth nežije v User
   - `Login/logout/token/permissions`
   - `AuthServiceInterface`
   - `SessionAuth` + `NullAuth` pre testovanie

2. **AuthMiddleware** - inject user identity do requestu

3. **AuthModule.php** - registrácia auth služieb

### ✅ Fáza 4: PermissionService
1. **Do Core alebo Auth modulu**
   - `can(User $user, Permission $perm): bool`
   - Na mieru aj pre MarkModule
   - "Mark is not a superuser" filozofia

### ✅ Fáza 5: ModuleContext (voliteľné)
1. **Každý modul dostane svoj kontext**
   - `ModuleContext(db, router, view, settings)`
   - Pomôže udržať moduly čistejšie
   - DI vo vlastnej kapsule

### ✅ Fáza 6: CoreModule
1. **Namiesto framework závislostí**
   - Obsahuje: `logger`, `settings`, `DI utils`
   - Voliteľne: `event bus`
   - Bez závislosti na konkrétnom frameworku

## 🧪 Testovacia stratégia

### Izolované testovanie modulov
```php
class UserModuleTest extends TestCase
{
    public function testModuleRegistration(): void
    {
        $mockRegistry = new MockRegistry();
        $userModule = new UserModule();

        $userModule->register($mockRegistry);

        $this->assertTrue($mockRegistry->has(UserCreateService::class));
        $this->assertTrue($mockRegistry->has(UserRepositoryInterface::class));
    }
}
```

### Mock implementácie
- `MockUserRepository` pre testy
- `NullAuthService` pre auth-free testy
- `InMemoryEventBus` pre event testy

## 📁 Cieľová štruktúra

```
src/
├── Shared/
│   ├── Domain/ValueObject/          # Spoločné ValueObjects
│   ├── Infrastructure/DI/           # RegistryInterface, MockRegistry
│   └── Application/                 # Spoločné aplikačné služby
├── Core/                           # CoreModule - framework-agnostic
│   ├── Domain/
│   ├── Application/
│   └── Infrastructure/
├── Module/
│   ├── User/                       # UserModule - čistý
│   ├── Auth/                       # AuthModule - samostatný
│   ├── Mark/                       # MarkModule - nezávislý
│   └── Article/                    # ArticleModule
└── tests/
    ├── Unit/Module/                # Izolované module testy
    ├── Integration/                # Cross-module testy
    └── TestDoubles/                # Mock implementácie
```

## 🎯 Výsledok Refactor 2.0

**Čistá modularita**:
- Každý modul je testovateľný v izolácii
- Žiadne cross-dependencies medzi modulmi
- Jasné rozhrania a abstrakcie

**Framework agnostic**:
- Moduly nezávislé od Slim 4
- Možnosť presunu na iný framework
- Čistá business logika

**Testovateľnosť**:
- Mock implementácie pre všetko
- Rýchle unit testy
- Izolované integration testy

## 🚦 Začíname

**Prvý krok**: RegistryInterface + MockRegistry
**Druhý krok**: UserModule refaktor
**Tretí krok**: AuthModule skeleton

Každý krok s testami a dokumentáciou! 🧪📚
