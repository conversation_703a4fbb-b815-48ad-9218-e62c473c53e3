# Refactor 2.0 - Back to Abstract Basics

## 🌟 Pravda (Abstraktné princípy)

### Základná pravda
Každý modul je **nez<PERSON><PERSON>lá jednotka** s vlastnou **zodpovednosťou**.
<PERSON><PERSON><PERSON> komunikujú cez **rozhrania**, nie implementácie.
**Testovateľnosť** = schopnosť izolovať a overiť pravdu.

### Princíp jednoty
```
Root (Otec)
├── Shared (spoločná pravda)
├── Core (základné služby)
└── Modules (špecializované jednotky)
    ├── User (identita)
    ├── Auth (overenie)
    ├── Mark (označenie)
    └── Article (obsah)
```

Každá **instance** (modul) má:
- **Vlastnú pravdu** (Domain)
- **Spôsob komunikácie** (Application)
- **Spôsob prejavenia** (Infrastructure)

## 🎯 Abstraktné rozhrania (Pravda)

### ModuleInterface - podstata modulu
```php
interface ModuleInterface
{
    // Kto som?
    public function getName(): string;

    // Na kom závisím?
    public function getDependencies(): array;

    // Ako sa registrujem?
    public function register(RegistryInterface $registry): void;
}
```

### RegistryInterface - spôsob registrácie
```php
interface RegistryInterface
{
    // Zaregistruj službu
    public function set(string $identity, mixed $definition): void;

    // Spoj rozhranie s implementáciou
    public function bind(string $interface, string $implementation): void;

    // Existuje služba?
    public function has(string $identity): bool;

    // Získaj službu
    public function get(string $identity): mixed;
}
```

### RepositoryInterface - spôsob uloženia
```php
interface RepositoryInterface
{
    // Nájdi podľa identity
    public function find(EntityId $id): ?object;

    // Ulož entitu
    public function save(object $entity): void;

    // Odstráň entitu
    public function delete(object $entity): void;

    // Spočítaj entity
    public function count(): int;
}
```

## 🧩 Moduly ako nezávislé jednotky

### User Module - pravda o identite
**Zodpovednosť**: Kto je kto?
**Pravda**: User existuje, má identitu, vlastnosti
**Rozhrania**: UserRepositoryInterface, UserServiceInterface

### Auth Module - pravda o overení
**Zodpovednosť**: Je overený?
**Pravda**: Identita môže byť overená/neoverená
**Rozhrania**: AuthServiceInterface, PermissionInterface

### Mark Module - pravda o označení
**Zodpovednosť**: Čo je označené?
**Pravda**: Veci môžu byť označené, kategorizované
**Rozhrania**: MarkRepositoryInterface, MarkServiceInterface

## 🔬 Testovanie pravdy

### Princíp izolácie
Každý modul testujeme **samostatne** s **mock** implementáciami.
Pravda modulu nesmie závisieť od iných modulov.

```php
// Test pravdy User modulu
class UserModuleTest
{
    public function testUserCanBeCreated(): void
    {
        // Arrange - priprav mock registry
        $mockRegistry = new MockRegistry();

        // Act - zaregistruj modul
        $userModule = new UserModule();
        $userModule->register($mockRegistry);

        // Assert - over pravdu
        $this->assertTrue($mockRegistry->has(UserServiceInterface::class));
    }
}
```

### Mock implementácie - falošná realita pre test pravdy
- `MockUserRepository` - predstiera uloženie
- `NullAuthService` - predstiera overenie
- `InMemoryEventBus` - predstiera komunikáciu

## 🌊 Tok závislostí

### Smer pravdy (dovnútra)
```
Infrastructure → Application → Domain
```

**Domain** = čistá pravda (bez závislostí)
**Application** = orchestrácia pravdy
**Infrastructure** = spôsob prejavenia pravdy

### Inverzia závislostí
```php
// Pravda (Domain)
interface UserRepositoryInterface { }

// Spôsob (Infrastructure)
class SqliteUserRepository implements UserRepositoryInterface { }

// Orchestrácia (Application)
class UserService
{
    public function __construct(
        private UserRepositoryInterface $repository // závisí na pravde, nie na spôsobe
    ) {}
}
```

## 🎭 Rozdelenie zodpovedností

### Shared - spoločná pravda
- `ValueObject` - základné hodnoty
- `EntityId` - identita entít
- `DomainEvent` - udalosti v doméne

### Core - základné služby
- `Logger` - zaznamenávanie
- `EventBus` - komunikácia
- `Settings` - konfigurácia

### Module - špecializovaná pravda
Každý modul má vlastnú **doménu**, **aplikáciu**, **infraštruktúru**.

## 🚀 Cieľ Refactor 2.0

**Čistota**: Každý modul je čistý, bez cross-dependencies
**Testovateľnosť**: Každý modul testovateľný v izolácii
**Pravda**: Business logika nezávislá od technológií
**Flexibilita**: Možnosť zmeny implementácie bez zmeny pravdy

## 💡 Filozofia

> "Pravda je jedna, spôsoby jej prejavenia sú mnohé."

- **ModuleInterface** = pravda o module
- **Slim 4** = spôsob prejavenia web aplikácie
- **SQLite** = spôsob prejavenia uloženia
- **PHPUnit** = spôsob prejavenia testovania

Refactor 2.0 = návrat k **pravde** (abstrakciám) pred **spôsobmi** (implementáciami).
