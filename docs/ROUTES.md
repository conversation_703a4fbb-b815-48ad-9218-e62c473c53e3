# Routes Documentation

This document describes the routing structure and best practices for the Fretboard Pattern Visualizer application.

## Route Structure

The application uses modular routing with separate route files for different sections:

```
config/routes/
├── web.php      # Main website pages
├── auth.php     # Authentication routes
├── user.php     # User management routes
├── article.php  # Article/blog routes
├── api.php      # API endpoints
├── admin.php    # Admin panel routes (legacy)
└── mark.php     # Mark panel routes (new)
```

## Mark Panel Routes

The Mark panel provides administrative functionality with the following route structure:

### Dashboard
- `GET /mark` - Mark panel dashboard

### User Management
```
GET    /mark/users           # User list with pagination
GET    /mark/users/create    # Create user form
POST   /mark/users/create    # Process user creation
GET    /mark/users/roles     # User roles management
GET    /mark/users/import    # User import page
GET    /mark/users/export    # User export functionality
GET    /mark/users/{id}/edit # Edit user form
POST   /mark/users/{id}/edit # Process user update
GET    /mark/users/{id}      # User details view
DELETE /mark/users/{id}      # Delete user
```

### System Management
```
GET /mark/settings    # System settings
GET /mark/logs        # System logs
GET /mark/analytics   # Analytics dashboard
GET /mark/files       # File manager
```

### Content Management
```
GET /mark/pages              # Page management
GET /mark/pages/create       # Create page form
GET /mark/articles           # Article management
GET /mark/articles/create    # Create article form
```

## Route Naming Convention

All routes follow a consistent naming pattern:

```php
->setName('mark-section-action');

Examples:
'mark-dashboard'        # /mark
'mark-users'           # /mark/users
'mark-user-create'     # /mark/users/create
'mark-user-edit'       # /mark/users/{id}/edit
'mark-user-view'       # /mark/users/{id}
'mark-settings'        # /mark/settings
```

## Route Order Best Practices

**CRITICAL:** Static routes MUST be defined before variable routes to prevent route shadowing.

### ✅ Correct Order:
```php
$users->get('/create', ...);     // Static route
$users->get('/roles', ...);      // Static route
$users->get('/import', ...);     // Static route
$users->get('/{id}/edit', ...);  // Variable route
$users->get('/{id}', ...);       // Variable route
```

### ❌ Incorrect Order:
```php
$users->get('/{id}', ...);       // Variable route catches everything
$users->get('/create', ...);     // Never reached - shadowed!
$users->get('/roles', ...);      // Never reached - shadowed!
```

### Route Shadowing Error:
```
Static route "/mark/users/roles" is shadowed by previously defined 
variable route "/mark/users/([^/]+)" for method "GET"
```

## Route Parameters

### Path Parameters
```php
// Single parameter
$users->get('/{id}', function ($request, $response, $args) {
    $userId = $args['id'];
    // ...
});

// Multiple parameters
$users->get('/{id}/posts/{postId}', function ($request, $response, $args) {
    $userId = $args['id'];
    $postId = $args['postId'];
    // ...
});
```

### Query Parameters
```php
// URL: /mark/users?page=2&limit=10&search=john
$queryParams = $request->getQueryParams();
$page = (int)($queryParams['page'] ?? 1);
$limit = (int)($queryParams['limit'] ?? 10);
$search = $queryParams['search'] ?? '';
```

## HTTP Methods

### Standard CRUD Operations
```php
GET    /resource        # List/Index
GET    /resource/create # Create form
POST   /resource/create # Store new resource
GET    /resource/{id}   # Show specific resource
GET    /resource/{id}/edit # Edit form
POST   /resource/{id}/edit # Update resource
DELETE /resource/{id}   # Delete resource
```

### RESTful API Endpoints
```php
GET    /api/users       # List users
POST   /api/users       # Create user
GET    /api/users/{id}  # Get user
PUT    /api/users/{id}  # Update user (full)
PATCH  /api/users/{id}  # Update user (partial)
DELETE /api/users/{id}  # Delete user
```

## Route Groups

Routes are organized using Slim's route groups for better organization:

```php
$app->group('/mark', function (RouteCollectorProxy $group) {
    // Dashboard
    $group->get('', ...)->setName('mark-dashboard');
    
    // User management group
    $group->group('/users', function (RouteCollectorProxy $users) {
        $users->get('', ...)->setName('mark-users');
        $users->get('/create', ...)->setName('mark-user-create');
        // ...
    });
    
    // Settings group
    $group->get('/settings', ...)->setName('mark-settings');
});
```

## Middleware

Routes can have middleware applied at different levels:

### Global Middleware
Applied to all routes in `config/middleware.php`

### Group Middleware
```php
$app->group('/mark', function (RouteCollectorProxy $group) {
    // Routes here
})->add(AuthMiddleware::class);
```

### Individual Route Middleware
```php
$group->get('/admin-only', function ($request, $response) {
    // Route handler
})->add(AdminMiddleware::class);
```

## Template Rendering

Mark panel routes use a consistent rendering pattern:

```php
$users->get('/create', function ($request, $response) {
    $renderer = $this->get(Slim\Views\PhpRenderer::class);
    
    // Get page content
    $pageContent = $renderer->fetch('themes/modern/pages/mark/user-create.php', [
        'title' => 'Create User',
        'data' => $someData
    ]);
    
    // Render with layout
    return $renderer->render($response, 'themes/modern/layouts/mark.php', [
        'title' => 'Create User',
        'pageTitle' => 'Create New User',
        'currentRoute' => 'mark-user-create',
        'content' => $pageContent
    ]);
});
```

## Error Handling

### 404 Not Found
Handled automatically by Slim when no route matches.

### 500 Server Error
Handled by error middleware in `config/middleware.php`.

### Custom Error Responses
```php
$users->get('/{id}', function ($request, $response, $args) {
    $userId = $args['id'];
    
    if (!$user = $userService->findById($userId)) {
        throw new HttpNotFoundException($request, 'User not found');
    }
    
    // Continue with normal processing
});
```

## Route Caching

For production environments, enable route caching:

```php
// In config/settings.php
'routerCacheFile' => $settings['cache']['path'] . '/routes.cache',
```

## Testing Routes

### Unit Testing
```php
public function testUserListRoute()
{
    $request = $this->createRequest('GET', '/mark/users');
    $response = $this->app->handle($request);
    
    $this->assertEquals(200, $response->getStatusCode());
}
```

### Integration Testing
```php
public function testCreateUserFlow()
{
    // Test GET form
    $response = $this->get('/mark/users/create');
    $this->assertEquals(200, $response->getStatusCode());
    
    // Test POST submission
    $response = $this->post('/mark/users/create', [
        'email' => '<EMAIL>',
        'username' => 'testuser'
    ]);
    $this->assertEquals(302, $response->getStatusCode());
}
```

## Best Practices

### 1. Route Organization
- Group related routes together
- Use descriptive route names
- Follow RESTful conventions

### 2. Parameter Validation
```php
$users->get('/{id}', function ($request, $response, $args) {
    $userId = $args['id'];
    
    // Validate UUID format
    if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $userId)) {
        throw new HttpBadRequestException($request, 'Invalid user ID format');
    }
    
    // Continue processing
});
```

### 3. Security
- Always validate input parameters
- Use CSRF protection for forms
- Implement proper authentication/authorization
- Sanitize output in templates

### 4. Performance
- Use route caching in production
- Minimize middleware overhead
- Implement proper database queries with pagination

## Troubleshooting

### Common Issues

1. **Route Shadowing**
   - Ensure static routes come before variable routes
   - Check route order in group definitions

2. **404 Errors**
   - Verify route is properly registered
   - Check HTTP method matches
   - Ensure middleware isn't blocking access

3. **Template Not Found**
   - Verify template path is correct
   - Check file permissions
   - Ensure template directory exists

### Debug Tools

Enable route debugging in development:

```php
// In config/settings.php
'displayErrorDetails' => true,
'logErrors' => true,
'logErrorDetails' => true,
```

## Adding New Routes

When adding new routes to the Mark panel:

1. **Add to mark.php**: Define route in `config/routes/mark.php`
2. **Follow naming**: Use `mark-section-action` pattern
3. **Order correctly**: Static before variable routes
4. **Create template**: Add corresponding template file
5. **Update navigation**: Add to sidebar if needed
6. **Test thoroughly**: Verify route works as expected

Example:
```php
// 1. Add route
$group->get('/reports', function ($request, $response) {
    // Route logic
})->setName('mark-reports');

// 2. Create template: templates/themes/modern/pages/mark/reports.php
// 3. Add to sidebar navigation if needed
// 4. Test the route
```
