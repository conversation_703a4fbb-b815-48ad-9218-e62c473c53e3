<?php

/**
 * Phinx configuration for SQLite with multiple databases.
 *
 * Usage:
 *   vendor/bin/phinx migrate -c config/phinx.php -e user
 *   vendor/bin/phinx migrate -c config/phinx.php -e article
 *   vendor/bin/phinx migrate -c config/phinx.php -e mark
 */

$dbBasePath = dirname(__DIR__) . '/var/db';

return [
    'paths' => [
        'migrations' => [
            'user' => dirname(__DIR__) . '/resources/migrations/user',
            'article' => dirname(__DIR__) . '/resources/migrations/article',
            'mark' => dirname(__DIR__) . '/resources/migrations/mark',
        ],
        'seeds' => dirname(__DIR__) . '/resources/seeds',
    ],
    'environments' => [
        'default_migration_table' => 'phinx_migration_log',
        'default_environment' => 'user',
        'user' => [
            'adapter' => 'sqlite',
            'name' => $dbBasePath . '/user.sqlite',
            'suffix' => '',
        ],
        'article' => [
            'adapter' => 'sqlite',
            'name' => $dbBasePath . '/article.sqlite',
            'suffix' => '',
        ],
        'mark' => [
            'adapter' => 'sqlite',
            'name' => $dbBasePath . '/mark.sqlite',
            'suffix' => '',
        ],
    ],
];
