#!/usr/bin/env php
<?php

declare(strict_types=1);

/**
 * Database setup script for the application.
 *
 * This script creates the necessary database files and runs migrations for all modules.
 */

// Make sure we're running from the project root
chdir(dirname(__DIR__));

// Display help if requested
if (in_array('--help', $argv) || in_array('-h', $argv)) {
    echo "Database Setup Script\n";
    echo "====================\n\n";
    echo "Usage: php bin/setup-databases [options]\n\n";
    echo "Options:\n";
    echo "  -h, --help     Show this help message\n";
    echo "  -v, --verbose  Show detailed output\n";
    exit(0);
}

$verbose = in_array('--verbose', $argv) || in_array('-v', $argv);

// Database directories and files
$dbDirs = [
    'user' => 'var/db/user',
    'mark' => 'var/db/mark',
    // 'article' => 'var/db/article',  // Uncomment when article module is ready
];

// Create database directories and files
foreach ($dbDirs as $module => $dbDir) {
    $dbFile = "$dbDir/database.sqlite";

    if (!is_dir($dbDir)) {
        if ($verbose) {
            echo "Creating directory: $dbDir\n";
        }
        if (!mkdir($dbDir, 0777, true) && !is_dir($dbDir)) {
            fwrite(STDERR, "Failed to create directory: $dbDir\n");
            exit(1);
        }
    }

    if (!file_exists($dbFile)) {
        if ($verbose) {
            echo "Creating database file: $dbFile\n";
        }
        if (!touch($dbFile)) {
            fwrite(STDERR, "Failed to create database file: $dbFile\n");
            exit(1);
        }
        chmod($dbFile, 0666);
    }
}

// Run migrations for each module
echo "Running database migrations...\n";

foreach (array_keys($dbDirs) as $module) {
    if ($verbose) {
        echo "\nMigrating $module database...\n";
    } else {
        echo "- $module: ";
    }

    $command = sprintf(
        'php vendor/bin/phinx migrate -c config/phinx.php -e %s %s',
        escapeshellarg($module),
        $verbose ? '-vvv' : '--no-ansi'
    );

    $output = [];
    $returnVar = 0;

    exec($command, $output, $returnVar);

    if ($verbose) {
        echo implode("\n", $output) . "\n";
    } else {
        echo $returnVar === 0 ? "OK\n" : "FAILED\n";
    }

    if ($returnVar !== 0) {
        fwrite(STDERR, "Error running migrations for $module\n");
        if (!$verbose) {
            fwrite(STDERR, "Run with -v for detailed output\n");
        }
        exit(1);
    }
}

echo "\nDatabase setup completed successfully!\n";

exit(0);
