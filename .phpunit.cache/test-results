{"version": 1, "defects": {"App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageAction": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateAction": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#too-short": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#too-long": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#empty-values": 8, "App\\Test\\TestCase\\User\\Create\\UserCreateActionTest::testUserSubmitCreateInvalid#empty-request-body": 8, "App\\Test\\TestCase\\User\\Delete\\UserDeleteActionTest::testUserSubmitDeleteAction": 8, "App\\Test\\TestCase\\User\\List\\ApiUserFetchListActionTest::testUserFetchListAction": 8, "App\\Test\\TestCase\\User\\List\\UserFetchListActionTest::testUserFetchList": 8, "App\\Test\\TestCase\\User\\Read\\UserReadPageActionTest::testUserReadPageAction": 8, "App\\Test\\TestCase\\User\\Read\\UserReadPageActionTest::testUserReadPageActionNotFound": 8, "App\\Test\\TestCase\\User\\Update\\UserUpdateActionTest::testUserSubmitUpdateAction": 8, "App\\Test\\TestCase\\User\\Update\\UserUpdateActionTest::testUserSubmitUpdateInvalid#0": 8, "App\\Test\\TestCase\\User\\Update\\UserUpdateActionTest::testUserSubmitUpdateInvalid#1": 8, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testCanBeCreated": 8, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testToArrayReturnsCorrectData": 8, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testToArrayUsesPublishedAtForDateWhenAvailable": 8, "Tests\\Unit\\Module\\User\\UserModuleTest::testCanGetRegisteredServices": 8, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testCanGetMarkService": 8, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanDispatchEvent": 8, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testHandlerIsCalledWhenEventIsDispatched": 8, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testMultipleHandlersAreCalled": 8, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanGetLastEventOfType": 8, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testHandlerExceptionDoesNotAffectOthers": 8, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanClearHistory": 8}, "times": {"App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageAction": 0.008, "App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageRedirectAction": 0.001, "App\\Test\\TestCase\\Home\\HomePageActionTest::testHomePageActionNotFound": 0.002, "App\\Test\\TestCase\\User\\List\\UserListPageActionTest::testUserListPageAction": 0.001, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testCanBeCreated": 0.066, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testToArrayReturnsCorrectData": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindLatestReturnsLimitedNumberOfArticles": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindByIdReturnsCorrectArticle": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindBySlugReturnsCorrectArticle": 0, "Tests\\Modules\\Article\\Infrastructure\\Persistence\\InMemoryArticleRepositoryTest::testFindBySlugReturnsNullForNonExistentSlug": 0, "Tests\\Modules\\Article\\Domain\\Entity\\ArticleTest::testToArrayUsesPublishedAtForDateWhenAvailable": 0, "Tests\\Unit\\Module\\User\\UserModuleTest::testModuleBasicProperties": 0.001, "Tests\\Unit\\Module\\User\\UserModuleTest::testModuleRegistersBasicServices": 0, "Tests\\Unit\\Module\\User\\UserModuleTest::testModuleRegistersActions": 0, "Tests\\Unit\\Module\\User\\UserModuleTest::testModuleRegistersCQRSServices": 0, "Tests\\Unit\\Module\\User\\UserModuleTest::testModuleIsIndependent": 0, "Tests\\Unit\\Module\\User\\UserModuleTest::testRegistrationDoesNotThrow": 0, "Tests\\Unit\\Module\\User\\UserModuleTest::testCanGetRegisteredServices": 0.008, "Tests\\Unit\\Module\\User\\UserModuleTest::testGetAllRegisteredServices": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testModuleBasicProperties": 0.002, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testModuleDependsOnUser": 0.001, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testModuleRegistersAuthServices": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testModuleRegistersMiddleware": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testModuleRegistersActions": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testModuleRegistersHelperServices": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testRegistrationDoesNotThrow": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testCanGetAuthService": 0.001, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testEnvironmentBasedImplementationSelection": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testGetAllRegisteredServices": 0, "Tests\\Unit\\Module\\Auth\\AuthModuleTest::testModuleDesignPrinciples": 0.001, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleBasicProperties": 0.002, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleDependsOnUser": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleRegistersRepository": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleRegistersMainServices": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleRegistersCQRSHandlers": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleRegistersControllers": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleRegistersApiActions": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testRegistrationDoesNotThrow": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testCanGetMarkService": 0.001, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testGetAllRegisteredServices": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleDesignPrinciples": 0, "Tests\\Unit\\Module\\Mark\\MarkModuleTest::testModuleResponsibility": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanDispatchEvent": 0.005, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanSubscribeToEvents": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testHandlerIsCalledWhenEventIsDispatched": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testMultipleHandlersAreCalled": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanUnsubscribeFromEvents": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanUnsubscribeAllHandlers": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanGetLastEventOfType": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testHandlerExceptionDoesNotAffectOthers": 0, "Tests\\Unit\\Shared\\Infrastructure\\Event\\InMemoryEventBusTest::testCanClearHistory": 0}}