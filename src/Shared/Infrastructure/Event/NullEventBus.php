<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\Event;

use App\Shared\Domain\Event\DomainEvent;
use App\Shared\Domain\Event\EventBusInterface;

/**
 * NullEventBus - spôsob pre testovanie bez skutočnej komunikácie
 * 
 * Implementuje EventBusInterface pre testovacie účely.
 * <PERSON><PERSON><PERSON><PERSON> "pošle" ale nič sa nestane.
 */
final class NullEventBus implements EventBusInterface
{
    /** @var DomainEvent[] */
    private array $dispatchedEvents = [];

    /** @var array<string, callable[]> */
    private array $subscribers = [];

    /**
     * Pošli správu do prázdnoty
     */
    public function dispatch(DomainEvent $event): void
    {
        // Len si zapamätaj že sa to stalo
        $this->dispatchedEvents[] = $event;
        
        // Ale nič nerobíme - je to null implementácia
    }

    /**
     * Počúvaj správy (ale nič sa nestane)
     */
    public function subscribe(string $eventType, callable $handler): void
    {
        if (!isset($this->subscribers[$eventType])) {
            $this->subscribers[$eventType] = [];
        }

        $this->subscribers[$eventType][] = $handler;
    }

    /**
     * Prestaň počúvať (ale aj tak si nepočúval)
     */
    public function unsubscribe(string $eventType, ?callable $handler = null): void
    {
        if ($handler === null) {
            unset($this->subscribers[$eventType]);
            return;
        }

        if (isset($this->subscribers[$eventType])) {
            $this->subscribers[$eventType] = array_filter(
                $this->subscribers[$eventType],
                fn($subscriber) => $subscriber !== $handler
            );
        }
    }

    /**
     * Aké správy počúvam? (žiadne, ale predstierame)
     */
    public function getSubscriptions(): array
    {
        return $this->subscribers;
    }

    /**
     * Počúvam tento typ správy? (nie, ale predstierame)
     */
    public function hasSubscribers(string $eventType): bool
    {
        return isset($this->subscribers[$eventType]) && !empty($this->subscribers[$eventType]);
    }

    /**
     * Koľko poslucháčov má tento typ správy? (0, ale predstierame)
     */
    public function getSubscriberCount(string $eventType): int
    {
        return isset($this->subscribers[$eventType]) ? count($this->subscribers[$eventType]) : 0;
    }

    /**
     * Získaj históriu odoslaných udalostí (pre testovanie)
     */
    public function getDispatchedEvents(): array
    {
        return $this->dispatchedEvents;
    }

    /**
     * Bola odoslaná udalosť tohto typu?
     */
    public function wasEventDispatched(string $eventType): bool
    {
        foreach ($this->dispatchedEvents as $event) {
            if ($event->getEventType() === $eventType) {
                return true;
            }
        }
        return false;
    }

    /**
     * Koľko udalostí tohto typu bolo odoslaných?
     */
    public function countEventsOfType(string $eventType): int
    {
        return count(array_filter(
            $this->dispatchedEvents,
            fn(DomainEvent $event) => $event->getEventType() === $eventType
        ));
    }

    /**
     * Vyčisti históriu (pre testovanie)
     */
    public function clear(): void
    {
        $this->dispatchedEvents = [];
        $this->subscribers = [];
    }
}
