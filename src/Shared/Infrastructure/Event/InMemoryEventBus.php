<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\Event;

use App\Shared\Domain\Event\DomainEvent;
use App\Shared\Domain\Event\EventBusInterface;

/**
 * InMemoryEventBus - spôsob komunikácie v pamäti
 * 
 * Implementuje EventBusInterface pre synchronnú komunikáciu.
 * Udalosti sa spracovávajú okamžite v rovnakom procese.
 */
final class InMemoryEventBus implements EventBusInterface
{
    /** @var array<string, callable[]> */
    private array $subscribers = [];

    /** @var DomainEvent[] */
    private array $dispatchedEvents = [];

    /**
     * Pošli správu do sveta
     */
    public function dispatch(DomainEvent $event): void
    {
        // Ulož udalosť do histórie
        $this->dispatchedEvents[] = $event;

        $eventType = $event->getEventType();

        // Ak nikto nepočúva, nevadí
        if (!isset($this->subscribers[$eventType])) {
            return;
        }

        // Zavolaj všetkých posluchá<PERSON>ov
        foreach ($this->subscribers[$eventType] as $handler) {
            try {
                $handler($event);
            } catch (\Throwable $e) {
                // V produkčnom prostredí by sme to zalogovali
                // Pre jednoduchosť teraz len pokračujeme
                error_log("Event handler failed: " . $e->getMessage());
            }
        }
    }

    /**
     * Počúvaj správy určitého typu
     */
    public function subscribe(string $eventType, callable $handler): void
    {
        if (!isset($this->subscribers[$eventType])) {
            $this->subscribers[$eventType] = [];
        }

        $this->subscribers[$eventType][] = $handler;
    }

    /**
     * Prestaň počúvať správy
     */
    public function unsubscribe(string $eventType, ?callable $handler = null): void
    {
        if (!isset($this->subscribers[$eventType])) {
            return;
        }

        if ($handler === null) {
            // Odstráň všetkých poslucháčov pre tento typ
            unset($this->subscribers[$eventType]);
            return;
        }

        // Odstráň konkrétneho poslucháča
        $this->subscribers[$eventType] = array_filter(
            $this->subscribers[$eventType],
            fn($subscriber) => $subscriber !== $handler
        );

        // Ak nezostali žiadni poslucháči, odstráň celý typ
        if (empty($this->subscribers[$eventType])) {
            unset($this->subscribers[$eventType]);
        }
    }

    /**
     * Aké správy počúvam?
     */
    public function getSubscriptions(): array
    {
        return $this->subscribers;
    }

    /**
     * Počúvam tento typ správy?
     */
    public function hasSubscribers(string $eventType): bool
    {
        return isset($this->subscribers[$eventType]) && !empty($this->subscribers[$eventType]);
    }

    /**
     * Koľko poslucháčov má tento typ správy?
     */
    public function getSubscriberCount(string $eventType): int
    {
        return isset($this->subscribers[$eventType]) ? count($this->subscribers[$eventType]) : 0;
    }

    /**
     * Získaj históriu odoslaných udalostí (pre testovanie)
     */
    public function getDispatchedEvents(): array
    {
        return $this->dispatchedEvents;
    }

    /**
     * Vyčisti históriu udalostí (pre testovanie)
     */
    public function clearHistory(): void
    {
        $this->dispatchedEvents = [];
    }

    /**
     * Vyčisti všetkých poslucháčov (pre testovanie)
     */
    public function clearSubscribers(): void
    {
        $this->subscribers = [];
    }

    /**
     * Kompletné vyčistenie (pre testovanie)
     */
    public function clear(): void
    {
        $this->clearHistory();
        $this->clearSubscribers();
    }

    /**
     * Získaj posledné odoslané udalosti určitého typu
     */
    public function getLastEventOfType(string $eventType): ?DomainEvent
    {
        $events = array_filter(
            $this->dispatchedEvents,
            fn(DomainEvent $event) => $event->getEventType() === $eventType
        );

        return empty($events) ? null : end($events);
    }

    /**
     * Spočítaj odoslané udalosti určitého typu
     */
    public function countEventsOfType(string $eventType): int
    {
        return count(array_filter(
            $this->dispatchedEvents,
            fn(DomainEvent $event) => $event->getEventType() === $eventType
        ));
    }
}
