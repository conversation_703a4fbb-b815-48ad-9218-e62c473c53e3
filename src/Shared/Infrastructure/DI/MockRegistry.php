<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\DI;

/**
 * MockRegistry - falošná realita pre test pravdy
 * 
 * Simuluje DI kontajner pre testovanie modulov v izolácii.
 * Ukladá služby v pamäti a poskytuje základnú resolúciu.
 */
final class MockRegistry implements RegistryInterface
{
    /** @var array<string, mixed> */
    private array $services = [];

    /** @var array<string, callable> */
    private array $factories = [];

    /** @var array<string, mixed> */
    private array $instances = [];

    /**
     * Zaregistruj službu
     */
    public function set(string $id, mixed $definition): void
    {
        $this->services[$id] = $definition;
    }

    /**
     * Zaregistruj službu pomocou autowiring
     */
    public function autowire(string $className): void
    {
        $this->services[$className] = $className;
    }

    /**
     * Spoj rozhranie s implementáciou
     */
    public function bind(string $interface, string $implementation): void
    {
        $this->services[$interface] = $implementation;
    }

    /**
     * Zaregistruj factory funkciu
     */
    public function factory(string $id, callable $factory): void
    {
        $this->factories[$id] = $factory;
    }

    /**
     * Zaregistruj singleton službu
     */
    public function singleton(string $id, mixed $definition): void
    {
        $this->services[$id] = $definition;
    }

    /**
     * Existuje služba?
     */
    public function has(string $id): bool
    {
        return isset($this->services[$id]) || isset($this->factories[$id]);
    }

    /**
     * Získaj službu
     */
    public function get(string $id): mixed
    {
        // Vráť cached inštanciu ak existuje
        if (isset($this->instances[$id])) {
            return $this->instances[$id];
        }

        // Použij factory ak je dostupná
        if (isset($this->factories[$id])) {
            $instance = ($this->factories[$id])();
            $this->instances[$id] = $instance;
            return $instance;
        }

        // Použij service definition
        if (isset($this->services[$id])) {
            $definition = $this->services[$id];
            
            if (is_string($definition) && class_exists($definition)) {
                // Jednoduchá inštanciácia triedy (bez skutočného autowiring pre mock)
                $instance = $this->createInstance($definition);
                $this->instances[$id] = $instance;
                return $instance;
            }
            
            if (is_callable($definition)) {
                $instance = $definition();
                $this->instances[$id] = $instance;
                return $instance;
            }
            
            return $definition;
        }

        throw new \RuntimeException("Service '{$id}' not found in mock registry");
    }

    /**
     * Vyčisti všetky zaregistrované služby (užitočné pre cleanup testov)
     */
    public function clear(): void
    {
        $this->services = [];
        $this->factories = [];
        $this->instances = [];
    }

    /**
     * Získaj všetky zaregistrované service ID (užitočné pre testovanie)
     * 
     * @return string[]
     */
    public function getRegisteredServices(): array
    {
        return array_keys(array_merge($this->services, $this->factories));
    }

    /**
     * Nastav mock inštanciu pre službu (užitočné pre testovanie)
     */
    public function setMock(string $id, mixed $mockInstance): void
    {
        $this->instances[$id] = $mockInstance;
        $this->services[$id] = $mockInstance;
    }

    /**
     * Vytvor inštanciu triedy (zjednodušené pre mock)
     */
    private function createInstance(string $className): object
    {
        try {
            // Pre mock účely vytvoríme inštanciu bez závislostí
            $reflection = new \ReflectionClass($className);
            
            if (!$reflection->isInstantiable()) {
                throw new \RuntimeException("Class '{$className}' is not instantiable");
            }

            $constructor = $reflection->getConstructor();
            
            if ($constructor === null) {
                // Trieda bez konštruktora
                return new $className();
            }

            $parameters = $constructor->getParameters();
            $args = [];

            foreach ($parameters as $parameter) {
                $type = $parameter->getType();
                
                if ($type instanceof \ReflectionNamedType && !$type->isBuiltin()) {
                    $typeName = $type->getName();
                    
                    // Skús získať závislosti z registry
                    if ($this->has($typeName)) {
                        $args[] = $this->get($typeName);
                    } else {
                        // Vytvor mock pre neznáme závislosti
                        $args[] = $this->createMockForType($typeName);
                    }
                } else {
                    // Pre základné typy použij default hodnoty
                    $args[] = $parameter->isDefaultValueAvailable() 
                        ? $parameter->getDefaultValue() 
                        : null;
                }
            }

            return new $className(...$args);
            
        } catch (\Exception $e) {
            throw new \RuntimeException(
                "Cannot create instance of '{$className}': " . $e->getMessage(),
                0,
                $e
            );
        }
    }

    /**
     * Vytvor mock pre typ (zjednodušené)
     */
    private function createMockForType(string $typeName): object
    {
        // Pre mock účely vrátime jednoduchý anonymous object
        return new class() {};
    }
}
