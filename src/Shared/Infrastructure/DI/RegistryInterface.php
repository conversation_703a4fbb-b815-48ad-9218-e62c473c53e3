<?php

declare(strict_types=1);

namespace App\Shared\Infrastructure\DI;

/**
 * Registry interface for module service registration.
 * 
 * This interface provides a clean abstraction for registering services
 * in the dependency injection container, allowing modules to be tested
 * in isolation with mock implementations.
 */
interface RegistryInterface
{
    /**
     * Register a service with the container.
     * 
     * @param string $id Service identifier (usually class name)
     * @param mixed $definition Service definition (factory, instance, etc.)
     */
    public function set(string $id, mixed $definition): void;

    /**
     * Register a service using autowiring.
     * 
     * @param string $className Class to autowire
     */
    public function autowire(string $className): void;

    /**
     * Bind an interface to a concrete implementation.
     * 
     * @param string $interface Interface class name
     * @param string $implementation Implementation class name
     */
    public function bind(string $interface, string $implementation): void;

    /**
     * Register a factory function for a service.
     * 
     * @param string $id Service identifier
     * @param callable $factory Factory function
     */
    public function factory(string $id, callable $factory): void;

    /**
     * Register a singleton service.
     * 
     * @param string $id Service identifier
     * @param mixed $definition Service definition
     */
    public function singleton(string $id, mixed $definition): void;

    /**
     * Check if a service is registered.
     * 
     * @param string $id Service identifier
     * @return bool
     */
    public function has(string $id): bool;

    /**
     * Get a service from the container.
     * 
     * @param string $id Service identifier
     * @return mixed
     */
    public function get(string $id): mixed;
}
