<?php

declare(strict_types=1);

namespace App\Shared\Domain\Event;

/**
 * DomainEvent - pravda o udalosti v doméne
 * 
 * Každá udalost má svoju identitu, čas a dáta.
 * Nezávisí od toho, kto ju počúva alebo spracováva.
 */
abstract readonly class DomainEvent
{
    public function __construct(
        private string $eventId,
        private \DateTimeImmutable $occurredAt,
        private array $eventData = []
    ) {}

    /**
     * Aká je moja identita?
     */
    public function getEventId(): string
    {
        return $this->eventId;
    }

    /**
     * Kedy som sa stala?
     */
    public function getOccurredAt(): \DateTimeImmutable
    {
        return $this->occurredAt;
    }

    /**
     * Aké sú moje dáta?
     */
    public function getEventData(): array
    {
        return $this->eventData;
    }

    /**
     * Aký je môj typ?
     */
    public function getEventType(): string
    {
        return static::class;
    }

    /**
     * Vytvor novú udalosť
     */
    protected static function create(array $eventData = []): static
    {
        return new static(
            eventId: bin2hex(random_bytes(16)),
            occurredAt: new \DateTimeImmutable(),
            eventData: $eventData
        );
    }

    /**
     * Som rovnaká ako iná udalosť?
     */
    public function equals(DomainEvent $other): bool
    {
        return $this->eventId === $other->eventId;
    }

    /**
     * Textová reprezentácia
     */
    public function toString(): string
    {
        return sprintf(
            '%s[%s] at %s',
            $this->getEventType(),
            $this->eventId,
            $this->occurredAt->format('Y-m-d H:i:s')
        );
    }

    /**
     * Serializácia do array
     */
    public function toArray(): array
    {
        return [
            'eventId' => $this->eventId,
            'eventType' => $this->getEventType(),
            'occurredAt' => $this->occurredAt->format(\DateTimeInterface::ATOM),
            'eventData' => $this->eventData,
        ];
    }
}
