<?php

declare(strict_types=1);

namespace App\Shared\Domain\Event;

/**
 * EventBusInterface - pravda o komunikácii cez udalosti
 * 
 * Umožňuje modulom komunikovať bez toho, aby o sebe vedeli.
 * Jeden pošle správu, iní ju počúvajú.
 */
interface EventBusInterface
{
    /**
     * Pošli správu do sveta
     * 
     * @param DomainEvent $event Udalosť na odoslanie
     */
    public function dispatch(DomainEvent $event): void;

    /**
     * Počúvaj správy určitého typu
     * 
     * @param string $eventType Typ udalosti (class name)
     * @param callable $handler Handler funkcia
     */
    public function subscribe(string $eventType, callable $handler): void;

    /**
     * Prestaň počúvať správy
     * 
     * @param string $eventType Typ udalosti
     * @param callable|null $handler Konkrétny handler alebo null pre všetky
     */
    public function unsubscribe(string $eventType, ?callable $handler = null): void;

    /**
     * <PERSON><PERSON><PERSON> správy počúvam?
     * 
     * @return array<string, callable[]> Mapa typov udalostí na handlery
     */
    public function getSubscriptions(): array;

    /**
     * Počúvam tento typ správy?
     * 
     * @param string $eventType Typ udalosti
     * @return bool True ak počúvam
     */
    public function hasSubscribers(string $eventType): bool;

    /**
     * Koľko poslucháčov má tento typ správy?
     * 
     * @param string $eventType Typ udalosti
     * @return int Počet poslucháčov
     */
    public function getSubscriberCount(string $eventType): int;
}
