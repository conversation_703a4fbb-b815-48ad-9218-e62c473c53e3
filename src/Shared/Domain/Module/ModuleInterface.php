<?php

declare(strict_types=1);

namespace App\Shared\Domain\Module;

use App\Shared\Infrastructure\DI\RegistryInterface;

/**
 * ModuleInterface - podstata modulu
 * 
 * Každý modul je nezávislá jednotka s vlastnou zodpovednosťou.
 * Komunikuje cez rozhrania, nie implementácie.
 */
interface ModuleInterface
{
    /**
     * Kto som?
     * 
     * @return string Jedinečný identifikátor modulu
     */
    public function getName(): string;

    /**
     * Na kom závisím?
     * 
     * @return string[] Zoznam názvov modulov, na ktorých závisím
     */
    public function getDependencies(): array;

    /**
     * Ako sa registrujem?
     * 
     * Zaregistruje všetky služby modulu do registry.
     * Toto je jediný bod kontaktu medzi modulom a DI kontajnerom.
     * 
     * @param RegistryInterface $registry Registry pre registráciu služieb
     */
    public function register(RegistryInterface $registry): void;
}
