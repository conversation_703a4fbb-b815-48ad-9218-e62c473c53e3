<?php

declare(strict_types=1);

namespace App\Core\Infrastructure\Service;

use App\Core\Domain\Service\ClockInterface;

/**
 * FixedClock - fixný čas pre testovanie
 * 
 * Implementácia ClockInterface s fixným časom.
 * Ideálne pre unit testy kde potrebujeme kontrolovať čas.
 */
final class FixedClock implements ClockInterface
{
    public function __construct(
        private \DateTimeImmutable $fixedTime
    ) {}

    /**
     * Aký je teraz čas? (v<PERSON><PERSON> fixný)
     */
    public function now(): \DateTimeImmutable
    {
        return $this->fixedTime;
    }

    /**
     * Aký je dnes dátum? (v<PERSON><PERSON> fixný)
     */
    public function today(): \DateTimeImmutable
    {
        return $this->fixedTime->setTime(0, 0, 0);
    }

    /**
     * Aký je čas v konkrétnej časovej zóne? (fixný, ale v inej zóne)
     */
    public function nowInTimezone(string $timezone): \DateTimeImmutable
    {
        return $this->fixedTime->setTimezone(new \DateTimeZone($timezone));
    }

    /**
     * Vytvor čas z timestamp (ignoruje fixný čas)
     */
    public function fromTimestamp(int $timestamp): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromFormat('U', (string)$timestamp);
    }

    /**
     * Vytvor čas z formátovaného stringu (ignoruje fixný čas)
     */
    public function fromFormat(string $format, string $datetime): \DateTimeImmutable
    {
        $result = \DateTimeImmutable::createFromFormat($format, $datetime);
        
        if ($result === false) {
            throw new \InvalidArgumentException("Cannot parse datetime '{$datetime}' with format '{$format}'");
        }
        
        return $result;
    }

    /**
     * Nastav nový fixný čas (pre testovanie)
     */
    public function setFixedTime(\DateTimeImmutable $time): void
    {
        $this->fixedTime = $time;
    }

    /**
     * Posuň fixný čas o interval (pre testovanie)
     */
    public function advance(\DateInterval $interval): void
    {
        $this->fixedTime = $this->fixedTime->add($interval);
    }

    /**
     * Získaj aktuálny fixný čas (pre testovanie)
     */
    public function getFixedTime(): \DateTimeImmutable
    {
        return $this->fixedTime;
    }
}
