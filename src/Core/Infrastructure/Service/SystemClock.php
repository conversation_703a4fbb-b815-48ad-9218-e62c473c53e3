<?php

declare(strict_types=1);

namespace App\Core\Infrastructure\Service;

use App\Core\Domain\Service\ClockInterface;

/**
 * SystemClock - skutočný systémový čas
 * 
 * Implementácia ClockInterface používajúca systémový čas.
 */
final class SystemClock implements ClockInterface
{
    /**
     * Aký je teraz čas?
     */
    public function now(): \DateTimeImmutable
    {
        return new \DateTimeImmutable();
    }

    /**
     * <PERSON>ký je dnes dátum?
     */
    public function today(): \DateTimeImmutable
    {
        return new \DateTimeImmutable('today');
    }

    /**
     * Aký je čas v konkrétnej časovej zóne?
     */
    public function nowInTimezone(string $timezone): \DateTimeImmutable
    {
        return new \DateTimeImmutable('now', new \DateTimeZone($timezone));
    }

    /**
     * Vytvor čas z timestamp
     */
    public function fromTimestamp(int $timestamp): \DateTimeImmutable
    {
        return \DateTimeImmutable::createFromFormat('U', (string)$timestamp);
    }

    /**
     * Vytvor čas z formátovaného stringu
     */
    public function fromFormat(string $format, string $datetime): \DateTimeImmutable
    {
        $result = \DateTimeImmutable::createFromFormat($format, $datetime);
        
        if ($result === false) {
            throw new \InvalidArgumentException("Cannot parse datetime '{$datetime}' with format '{$format}'");
        }
        
        return $result;
    }
}
