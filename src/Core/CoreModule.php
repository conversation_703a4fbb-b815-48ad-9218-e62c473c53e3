<?php

declare(strict_types=1);

namespace App\Core;

use App\Shared\Domain\Module\ModuleInterface;
use App\Shared\Infrastructure\DI\RegistryInterface;
use App\Shared\Domain\Event\EventBusInterface;
use App\Shared\Infrastructure\Event\InMemoryEventBus;
use App\Shared\Infrastructure\Event\NullEventBus;

/**
 * CoreModule - pravda o základných službách
 * 
 * Nezávislá jednotka poskytujúca základné služby pre všetky moduly.
 * Logger, EventBus, Settings, Clock - veci ktoré potrebuje každý,
 * ale nezávisia na žiadnom konkrétnom module.
 */
final class CoreModule implements ModuleInterface
{
    /**
     * Kto som?
     */
    public function getName(): string
    {
        return 'core';
    }

    /**
     * Na kom závisím?
     * 
     * Core modul je základný - nezávisí na žiadnom inom module.
     * Je to koreň stromu z<PERSON>tí.
     */
    public function getDependencies(): array
    {
        return []; // Nezávislý - je to základ
    }

    /**
     * Ako sa registrujem?
     * 
     * Registruje základné služby potrebné pre celú aplikáciu.
     * Tieto služby sú framework-agnostic a môžu byť použité kdekoľvek.
     */
    public function register(RegistryInterface $registry): void
    {
        // EventBus - komunikácia medzi modulmi
        $this->registerEventBus($registry);

        // Logger - zaznamenávanie udalostí
        $this->registerLogger($registry);

        // Settings - konfigurácia aplikácie
        $this->registerSettings($registry);

        // Clock - práca s časom
        $this->registerClock($registry);

        // Utility služby
        $this->registerUtilities($registry);
    }

    /**
     * Registruj EventBus implementácie
     */
    private function registerEventBus(RegistryInterface $registry): void
    {
        // InMemoryEventBus ako hlavná implementácia
        $registry->factory(InMemoryEventBus::class, function () {
            return new InMemoryEventBus();
        });

        // NullEventBus pre testovanie
        $registry->factory(NullEventBus::class, function () {
            return new NullEventBus();
        });

        // Bind interface na správnu implementáciu podľa prostredia
        $registry->factory(EventBusInterface::class, function () use ($registry) {
            $environment = $_ENV['APP_ENV'] ?? 'production';
            
            if ($environment === 'test') {
                return $registry->get(NullEventBus::class);
            }
            
            return $registry->get(InMemoryEventBus::class);
        });
    }

    /**
     * Registruj Logger implementácie
     */
    private function registerLogger(RegistryInterface $registry): void
    {
        // PSR-3 Logger interface
        $registry->factory(\Psr\Log\LoggerInterface::class, function () {
            // Jednoduchý file logger
            return new \App\Core\Infrastructure\Logger\FileLogger(
                logPath: $_ENV['LOG_PATH'] ?? 'var/log/app.log',
                logLevel: $_ENV['LOG_LEVEL'] ?? 'info'
            );
        });

        // Null logger pre testovanie
        $registry->factory('logger.null', function () {
            return new \Psr\Log\NullLogger();
        });
    }

    /**
     * Registruj Settings služby
     */
    private function registerSettings(RegistryInterface $registry): void
    {
        // Settings repository
        $registry->factory('settings', function () {
            return new \App\Core\Application\Service\Settings([
                'app.name' => $_ENV['APP_NAME'] ?? 'Mark Slim4',
                'app.env' => $_ENV['APP_ENV'] ?? 'production',
                'app.debug' => filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN),
                'db.default' => $_ENV['DB_DEFAULT'] ?? 'sqlite',
                'cache.enabled' => filter_var($_ENV['CACHE_ENABLED'] ?? true, FILTER_VALIDATE_BOOLEAN),
            ]);
        });
    }

    /**
     * Registruj Clock služby
     */
    private function registerClock(RegistryInterface $registry): void
    {
        // System clock
        $registry->factory(\App\Core\Domain\Service\ClockInterface::class, function () {
            return new \App\Core\Infrastructure\Service\SystemClock();
        });

        // Fixed clock pre testovanie
        $registry->factory('clock.fixed', function () {
            return new \App\Core\Infrastructure\Service\FixedClock(
                new \DateTimeImmutable('2024-01-01 12:00:00')
            );
        });
    }

    /**
     * Registruj utility služby
     */
    private function registerUtilities(RegistryInterface $registry): void
    {
        // UUID generator
        $registry->factory('uuid.generator', function () {
            return new \App\Core\Infrastructure\Service\UuidGenerator();
        });

        // Hash service
        $registry->factory('hash.service', function () {
            return new \App\Core\Infrastructure\Service\HashService();
        });

        // Validation service
        $registry->factory('validation.service', function () {
            return new \App\Core\Application\Service\ValidationService();
        });
    }
}
