<?php

declare(strict_types=1);

namespace App\Core\Domain\Service;

/**
 * ClockInterface - pravda o čase
 * 
 * Abstrakcia pre prácu s časom.
 * Umožňuje testovanie s fixným časom.
 */
interface ClockInterface
{
    /**
     * Aký je teraz čas?
     */
    public function now(): \DateTimeImmutable;

    /**
     * Aký je dnes dátum?
     */
    public function today(): \DateTimeImmutable;

    /**
     * Aký je čas v konkrétnej časovej zóne?
     */
    public function nowInTimezone(string $timezone): \DateTimeImmutable;

    /**
     * Vytvor čas z timestamp
     */
    public function fromTimestamp(int $timestamp): \DateTimeImmutable;

    /**
     * Vytvor čas z formátovaného stringu
     */
    public function fromFormat(string $format, string $datetime): \DateTimeImmutable;
}
