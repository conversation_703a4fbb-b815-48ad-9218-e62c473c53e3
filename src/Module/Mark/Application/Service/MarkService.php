<?php

declare(strict_types=1);

namespace App\Module\Mark\Application\Service;

use App\Module\Mark\Domain\Entity\Mark;
use App\Module\Mark\Domain\Repository\MarkRepositoryInterface;
use App\Module\Mark\Domain\Service\MarkServiceInterface;
use App\Module\Mark\Domain\ValueObject\MarkId;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * MarkService - implementácia MarkServiceInterface
 * 
 * Orchestruje Mark operácie cez repository.
 * Obsahuje business logiku pre prácu s označeniami.
 */
final readonly class MarkService implements MarkServiceInterface
{
    public function __construct(
        private MarkRepositoryInterface $markRepository
    ) {}

    /**
     * Vytvor nové označenie
     */
    public function createMark(UserId $userId, string $title, string $content, bool $isPublic = false): MarkId
    {
        // Validácia dát
        $this->validateMarkData($title, $content);

        // Vytvor Mark entitu
        $markId = MarkId::generate();
        $mark = new Mark(
            $markId,
            $userId,
            $title,
            $content,
            $isPublic
        );

        // Ulož do repository
        $this->markRepository->save($mark);

        return $markId;
    }

    /**
     * Nájdi označenie podľa ID
     */
    public function findMark(MarkId $id): ?Mark
    {
        return $this->markRepository->find($id);
    }

    /**
     * Nájdi označenia používateľa
     */
    public function findMarksByUser(UserId $userId, int $limit = 10, int $offset = 0): array
    {
        return $this->markRepository->findByUser($userId, $limit, $offset);
    }

    /**
     * Nájdi verejné označenia
     */
    public function findPublicMarks(int $limit = 10, int $offset = 0): array
    {
        return $this->markRepository->findPublic($limit, $offset);
    }

    /**
     * Aktualizuj označenie
     */
    public function updateMark(MarkId $id, string $title, string $content, bool $isPublic = false): bool
    {
        $mark = $this->markRepository->find($id);
        if (!$mark) {
            return false;
        }

        // Validácia dát
        $this->validateMarkData($title, $content);

        // Aktualizuj entitu
        $mark->update($title, $content, $isPublic);

        // Ulož zmeny
        $this->markRepository->save($mark);

        return true;
    }

    /**
     * Odstráň označenie
     */
    public function deleteMark(MarkId $id): bool
    {
        $mark = $this->markRepository->find($id);
        if (!$mark) {
            return false;
        }

        $this->markRepository->delete($mark);
        return true;
    }

    /**
     * Urob označenie verejným
     */
    public function makeMarkPublic(MarkId $id): bool
    {
        $mark = $this->markRepository->find($id);
        if (!$mark) {
            return false;
        }

        $mark->makePublic();
        $this->markRepository->save($mark);

        return true;
    }

    /**
     * Urob označenie súkromným
     */
    public function makeMarkPrivate(MarkId $id): bool
    {
        $mark = $this->markRepository->find($id);
        if (!$mark) {
            return false;
        }

        $mark->makePrivate();
        $this->markRepository->save($mark);

        return true;
    }

    /**
     * Spočítaj označenia používateľa
     */
    public function countMarksByUser(UserId $userId): int
    {
        return $this->markRepository->countByUser($userId);
    }

    /**
     * Spočítaj verejné označenia
     */
    public function countPublicMarks(): int
    {
        return $this->markRepository->countPublic();
    }

    /**
     * Môže používateľ pristupovať k označeniu?
     */
    public function canUserAccessMark(MarkId $markId, UserId $userId): bool
    {
        $mark = $this->markRepository->find($markId);
        if (!$mark) {
            return false;
        }

        // Používateľ môže pristupovať k svojím označeniam alebo k verejným
        return $mark->getUserId()->equals($userId) || $mark->isPublic();
    }

    /**
     * Validuj dáta označenia
     * 
     * @param string $title
     * @param string $content
     * @throws \InvalidArgumentException
     */
    private function validateMarkData(string $title, string $content): void
    {
        if (empty(trim($title))) {
            throw new \InvalidArgumentException('Názov označenia je povinný');
        }

        if (empty(trim($content))) {
            throw new \InvalidArgumentException('Obsah označenia je povinný');
        }

        if (strlen($title) > 255) {
            throw new \InvalidArgumentException('Názov označenia je príliš dlhý (max 255 znakov)');
        }

        if (strlen($content) > 10000) {
            throw new \InvalidArgumentException('Obsah označenia je príliš dlhý (max 10000 znakov)');
        }
    }
}
