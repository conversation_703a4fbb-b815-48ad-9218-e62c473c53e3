<?php

declare(strict_types=1);

namespace App\Module\Mark;

use App\Shared\Domain\Module\ModuleInterface;
use App\Shared\Infrastructure\DI\RegistryInterface;
use App\Module\Mark\Domain\Repository\MarkRepositoryInterface;
use App\Module\Mark\Domain\Service\MarkServiceInterface;
use App\Module\Mark\Application\Service\MarkService;
use App\Infrastructure\Persistence\Mark\DbMarkRepository;

/**
 * MarkModule - pravda o označení
 * 
 * Nezávislá jednotka zodpovedná za správu označení.
 * Mark = rozl<PERSON><PERSON><PERSON>e, kategorizácia, označenie dôležitosti.
 * Vie čo je označené, nezávisle od toho kto označuje.
 */
final class MarkModule implements ModuleInterface
{
    /**
     * Kto som?
     */
    public function getName(): string
    {
        return 'mark';
    }

    /**
     * Na kom závisím?
     * 
     * Mark modul závisí na User module - potrebuje vedieť kto označuje.
     * Nezávisí na Auth module - to je zodpovednosť vyšších vrstiev.
     */
    public function getDependencies(): array
    {
        return ['user']; // Potrebujem UserId pre označenia
    }

    /**
     * Ako sa registrujem?
     * 
     * Registruje všetky služby potrebné pre Mark modul.
     * Používa len abstraktné rozhrania - žiadne konkrétne závislosti.
     */
    public function register(RegistryInterface $registry): void
    {
        // Repository - spôsob uloženia Mark entít
        $this->registerRepository($registry);

        // Service - orchestrácia Mark operácií  
        $this->registerServices($registry);

        // CQRS - Commands & Queries
        $this->registerCQRS($registry);

        // HTTP Controllers (voliteľné)
        $this->registerControllers($registry);
    }

    /**
     * Registruj repository implementácie
     */
    private function registerRepository(RegistryInterface $registry): void
    {
        // DbMarkRepository ako hlavná implementácia
        $registry->factory(DbMarkRepository::class, function () use ($registry) {
            return new DbMarkRepository(
                $registry->get('db.connection.mark') // Mark má vlastnú databázu
            );
        });

        // Bind interface na DbMarkRepository
        $registry->bind(
            MarkRepositoryInterface::class,
            DbMarkRepository::class
        );
    }

    /**
     * Registruj hlavné služby
     */
    private function registerServices(RegistryInterface $registry): void
    {
        // MarkService ako hlavná implementácia
        $registry->factory(MarkService::class, function () use ($registry) {
            return new MarkService(
                $registry->get(MarkRepositoryInterface::class)
            );
        });

        // Bind interface na MarkService
        $registry->bind(
            MarkServiceInterface::class,
            MarkService::class
        );
    }

    /**
     * Registruj CQRS handlers
     */
    private function registerCQRS(RegistryInterface $registry): void
    {
        // Command handlers
        $registry->factory(
            \App\Module\Mark\Application\Command\CreateMarkHandler::class,
            function () use ($registry) {
                return new \App\Module\Mark\Application\Command\CreateMarkHandler(
                    $registry->get(MarkRepositoryInterface::class)
                );
            }
        );

        // Query handlers
        $registry->factory(
            \App\Module\Mark\Application\Query\GetMarksHandler::class,
            function () use ($registry) {
                return new \App\Module\Mark\Application\Query\GetMarksHandler(
                    $registry->get(MarkRepositoryInterface::class)
                );
            }
        );

        // Commands & Queries (value objects)
        $registry->autowire(\App\Module\Mark\Application\Command\CreateMarkCommand::class);
        $registry->autowire(\App\Module\Mark\Application\Query\GetMarksQuery::class);
    }

    /**
     * Registruj HTTP controllers
     */
    private function registerControllers(RegistryInterface $registry): void
    {
        // MarkController
        $registry->factory(
            \App\Module\Mark\Infrastructure\Http\Controller\MarkController::class,
            function () use ($registry) {
                return new \App\Module\Mark\Infrastructure\Http\Controller\MarkController(
                    $registry // Predáme celý registry ako kontajner
                );
            }
        );

        // API Actions (ak existujú)
        $this->registerApiActions($registry);
    }

    /**
     * Registruj API actions
     */
    private function registerApiActions(RegistryInterface $registry): void
    {
        // CreateMarkAction
        $registry->factory('action.mark.create', function () use ($registry) {
            return new class($registry) {
                public function __construct(private $registry) {}
                
                public function __invoke($request, $response, $args) {
                    $markService = $this->registry->get(MarkServiceInterface::class);
                    // Implementation would go here
                    return $response;
                }
            };
        });

        // ListMarksAction
        $registry->factory('action.mark.list', function () use ($registry) {
            return new class($registry) {
                public function __construct(private $registry) {}
                
                public function __invoke($request, $response, $args) {
                    $markService = $this->registry->get(MarkServiceInterface::class);
                    // Implementation would go here
                    return $response;
                }
            };
        });

        // UpdateMarkAction
        $registry->factory('action.mark.update', function () use ($registry) {
            return new class($registry) {
                public function __construct(private $registry) {}
                
                public function __invoke($request, $response, $args) {
                    $markService = $this->registry->get(MarkServiceInterface::class);
                    // Implementation would go here
                    return $response;
                }
            };
        });

        // DeleteMarkAction
        $registry->factory('action.mark.delete', function () use ($registry) {
            return new class($registry) {
                public function __construct(private $registry) {}
                
                public function __invoke($request, $response, $args) {
                    $markService = $this->registry->get(MarkServiceInterface::class);
                    // Implementation would go here
                    return $response;
                }
            };
        });
    }
}
