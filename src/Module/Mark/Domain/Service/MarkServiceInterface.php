<?php

declare(strict_types=1);

namespace App\Module\Mark\Domain\Service;

use App\Module\Mark\Domain\Entity\Mark;
use App\Module\Mark\Domain\ValueObject\MarkId;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * MarkServiceInterface - pravda o <PERSON> s<PERSON>
 * 
 * Definuje čo môžeme robiť s označeniami.
 * Mark = rozlíšenie, kategorizácia, označenie dôležitosti.
 * Nezávisí od implementácie - len definuje pravdu.
 */
interface MarkServiceInterface
{
    /**
     * Vytvor nové označenie
     * 
     * @param UserId $userId Kto označuje
     * @param string $title Názov označenia
     * @param string $content Obsah označenia
     * @param bool $isPublic Je verejné?
     * @return MarkId ID vytvoreného označenia
     */
    public function createMark(UserId $userId, string $title, string $content, bool $isPublic = false): MarkId;

    /**
     * Nájdi označenie podľa ID
     * 
     * @param MarkId $id ID označenia
     * @return Mark|null Označenie alebo null ak neexistuje
     */
    public function findMark(MarkId $id): ?Mark;

    /**
     * Nájdi označenia používateľa
     * 
     * @param UserId $userId ID používateľa
     * @param int $limit Limit počtu označení
     * @param int $offset Offset pre stránkovanie
     * @return Mark[] Zoznam označení
     */
    public function findMarksByUser(UserId $userId, int $limit = 10, int $offset = 0): array;

    /**
     * Nájdi verejné označenia
     * 
     * @param int $limit Limit počtu označení
     * @param int $offset Offset pre stránkovanie
     * @return Mark[] Zoznam verejných označení
     */
    public function findPublicMarks(int $limit = 10, int $offset = 0): array;

    /**
     * Aktualizuj označenie
     * 
     * @param MarkId $id ID označenia
     * @param string $title Nový názov
     * @param string $content Nový obsah
     * @param bool $isPublic Je verejné?
     * @return bool True ak bolo aktualizované
     */
    public function updateMark(MarkId $id, string $title, string $content, bool $isPublic = false): bool;

    /**
     * Odstráň označenie
     * 
     * @param MarkId $id ID označenia
     * @return bool True ak bolo odstránené
     */
    public function deleteMark(MarkId $id): bool;

    /**
     * Urob označenie verejným
     * 
     * @param MarkId $id ID označenia
     * @return bool True ak bolo zmenené
     */
    public function makeMarkPublic(MarkId $id): bool;

    /**
     * Urob označenie súkromným
     * 
     * @param MarkId $id ID označenia
     * @return bool True ak bolo zmenené
     */
    public function makeMarkPrivate(MarkId $id): bool;

    /**
     * Spočítaj označenia používateľa
     * 
     * @param UserId $userId ID používateľa
     * @return int Počet označení
     */
    public function countMarksByUser(UserId $userId): int;

    /**
     * Spočítaj verejné označenia
     * 
     * @return int Počet verejných označení
     */
    public function countPublicMarks(): int;

    /**
     * Môže používateľ pristupovať k označeniu?
     * 
     * @param MarkId $markId ID označenia
     * @param UserId $userId ID používateľa
     * @return bool True ak môže pristupovať
     */
    public function canUserAccessMark(MarkId $markId, UserId $userId): bool;
}
