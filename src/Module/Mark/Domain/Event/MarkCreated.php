<?php

declare(strict_types=1);

namespace App\Module\Mark\Domain\Event;

use App\Shared\Domain\Event\DomainEvent;
use App\Module\Mark\Domain\ValueObject\MarkId;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * MarkCreated - udalosť "Označenie bolo vytvorené"
 *
 * Oznamuje svetu, že niečo bolo označené ako dôležité.
 */
final readonly class MarkCreated extends DomainEvent
{
    /**
     * Vytvor udalosť o vytvorení označenia
     */
    public static function occur(MarkId $markId, UserId $userId, string $title, bool $isPublic = false): self
    {
        return self::create([
            'markId' => (string)$markId,
            'userId' => (string)$userId,
            'title' => $title,
            'isPublic' => $isPublic,
        ]);
    }

    /**
     * Aké označenie bolo vytvorené?
     */
    public function getMarkId(): MarkId
    {
        return MarkId::fromString($this->getEventData()['markId']);
    }

    /**
     * Kto ho vytvoril?
     */
    public function getUserId(): UserId
    {
        return UserId::fromString($this->getEventData()['userId']);
    }

    /**
     * Aký má názov?
     */
    public function getTitle(): string
    {
        return $this->getEventData()['title'];
    }

    /**
     * Je verejné?
     */
    public function isPublic(): bool
    {
        return $this->getEventData()['isPublic'];
    }
}
