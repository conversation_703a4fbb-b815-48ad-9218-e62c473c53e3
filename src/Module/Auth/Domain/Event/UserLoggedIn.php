<?php

declare(strict_types=1);

namespace App\Module\Auth\Domain\Event;

use App\Shared\Domain\Event\DomainEvent;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * UserLoggedIn - udalosť "Používateľ sa prihlásil"
 *
 * Oz<PERSON>uje svetu, že identita bola overená.
 */
final readonly class UserLoggedIn extends DomainEvent
{
    /**
     * Vytvor udalosť o prihlásení
     */
    public static function occur(UserId $userId, string $tokenId, array $permissions = []): self
    {
        return self::create([
            'userId' => (string)$userId,
            'tokenId' => $tokenId,
            'permissions' => $permissions,
        ]);
    }

    /**
     * Kto sa prihlásil?
     */
    public function getUserId(): UserId
    {
        return UserId::fromString($this->getEventData()['userId']);
    }

    /**
     * Aký má token?
     */
    public function getTokenId(): string
    {
        return $this->getEventData()['tokenId'];
    }

    /**
     * <PERSON><PERSON><PERSON> má oprávnenia?
     */
    public function getPermissions(): array
    {
        return $this->getEventData()['permissions'];
    }
}
