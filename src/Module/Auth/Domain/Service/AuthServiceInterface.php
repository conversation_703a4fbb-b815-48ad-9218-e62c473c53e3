<?php

declare(strict_types=1);

namespace App\Module\Auth\Domain\Service;

use App\Module\Auth\Domain\ValueObject\AuthToken;
use App\Module\Auth\Domain\ValueObject\Permission;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * AuthServiceInterface - pravda o overení
 * 
 * Definuje čo znamená byť overený v systéme.
 * Nezávisí od spôsobu overenia (session, JWT, atď.)
 */
interface AuthServiceInterface
{
    /**
     * Kto si?
     * 
     * Overí identitu a vráti token ak je platná.
     * 
     * @param string $identity Identifikátor (email, username, atď.)
     * @param string $credential Overovací údaj (password, atď.)
     * @return AuthToken|null Token ak je overenie úspešné, null ak nie
     */
    public function authenticate(string $identity, string $credential): ?AuthToken;

    /**
     * Môžeš?
     * 
     * Overí či token má oprávnenie na vykonanie akcie.
     * 
     * @param AuthToken $token Token overenia
     * @param Permission $permission Požadované oprávnenie
     * @return bool True ak má oprávnenie, false ak nie
     */
    public function authorize(AuthToken $token, Permission $permission): bool;

    /**
     * Odíď.
     * 
     * Zruší platnosť tokenu.
     * 
     * @param AuthToken $token Token na zrušenie
     * @return void
     */
    public function logout(AuthToken $token): void;

    /**
     * Si ešte tu?
     * 
     * Overí či je token ešte platný.
     * 
     * @param AuthToken $token Token na overenie
     * @return bool True ak je platný, false ak nie
     */
    public function isTokenValid(AuthToken $token): bool;

    /**
     * Obnov sa.
     * 
     * Obnoví token s novou expirációu.
     * 
     * @param AuthToken $token Starý token
     * @return AuthToken|null Nový token alebo null ak sa nepodarilo obnoviť
     */
    public function refreshToken(AuthToken $token): ?AuthToken;

    /**
     * Kto si teraz?
     * 
     * Získa aktuálne overenú identitu.
     * 
     * @return AuthToken|null Aktuálny token alebo null ak nie je nikto overený
     */
    public function getCurrentToken(): ?AuthToken;

    /**
     * Aké máš oprávnenia?
     * 
     * Získa všetky oprávnenia pre token.
     * 
     * @param AuthToken $token Token
     * @return Permission[] Zoznam oprávnení
     */
    public function getTokenPermissions(AuthToken $token): array;
}
