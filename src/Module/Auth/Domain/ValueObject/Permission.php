<?php

declare(strict_types=1);

namespace App\Module\Auth\Domain\ValueObject;

/**
 * Permission - pravda o oprávnení
 * 
 * Reprezentuje čo môže byť vykonané v systéme.
 * Nezávisí od toho, kto to vykonáva.
 */
final readonly class Permission
{
    public function __construct(
        private string $action,
        private ?string $resource = null,
        private ?string $context = null
    ) {
        if (empty($action)) {
            throw new \InvalidArgumentException('Action nemôže byť prázdny');
        }
    }

    /**
     * Čo môže byť vykonané?
     */
    public function getAction(): string
    {
        return $this->action;
    }

    /**
     * Na čom môže byť vykonané?
     */
    public function getResource(): ?string
    {
        return $this->resource;
    }

    /**
     * V akom kontexte?
     */
    public function getContext(): ?string
    {
        return $this->context;
    }

    /**
     * Textová reprezentácia oprávnenia
     */
    public function toString(): string
    {
        $parts = [$this->action];
        
        if ($this->resource !== null) {
            $parts[] = $this->resource;
        }
        
        if ($this->context !== null) {
            $parts[] = $this->context;
        }
        
        return implode(':', $parts);
    }

    /**
     * Som rovnaké ako iné oprávnenie?
     */
    public function equals(Permission $other): bool
    {
        return $this->toString() === $other->toString();
    }

    /**
     * Vytvor oprávnenie z textu
     */
    public static function fromString(string $permission): self
    {
        $parts = explode(':', $permission);
        
        return new self(
            action: $parts[0],
            resource: $parts[1] ?? null,
            context: $parts[2] ?? null
        );
    }

    /**
     * Predefinované oprávnenia
     */
    public static function userCreate(): self
    {
        return new self('create', 'user');
    }

    public static function userRead(): self
    {
        return new self('read', 'user');
    }

    public static function userUpdate(): self
    {
        return new self('update', 'user');
    }

    public static function userDelete(): self
    {
        return new self('delete', 'user');
    }

    public static function markCreate(): self
    {
        return new self('create', 'mark');
    }

    public static function markRead(): self
    {
        return new self('read', 'mark');
    }

    public static function markUpdate(): self
    {
        return new self('update', 'mark');
    }

    public static function markDelete(): self
    {
        return new self('delete', 'mark');
    }

    public static function adminAccess(): self
    {
        return new self('access', 'admin');
    }
}
