<?php

declare(strict_types=1);

namespace App\Module\Auth\Domain\ValueObject;

use App\Module\User\Domain\ValueObject\UserId;

/**
 * AuthToken - pravda o overení
 * 
 * Reprezentuje overenú identitu v systéme.
 * Nehovorí ako je uložen<PERSON>, len čo reprezentuje.
 */
final readonly class AuthToken
{
    public function __construct(
        private string $tokenId,
        private UserId $userId,
        private \DateTimeImmutable $issuedAt,
        private ?\DateTimeImmutable $expiresAt = null,
        private array $permissions = []
    ) {}

    /**
     * Kto som?
     */
    public function getTokenId(): string
    {
        return $this->tokenId;
    }

    /**
     * Koho reprezentujem?
     */
    public function getUserId(): UserId
    {
        return $this->userId;
    }

    /**
     * Kedy som bol vytvorený?
     */
    public function getIssuedAt(): \DateTimeImmutable
    {
        return $this->issuedAt;
    }

    /**
     * Kedy expiruje moja platnosť?
     */
    public function getExpiresAt(): ?\DateTimeImmutable
    {
        return $this->expiresAt;
    }

    /**
     * Aké mám oprávnenia?
     */
    public function getPermissions(): array
    {
        return $this->permissions;
    }

    /**
     * Som ešte platný?
     */
    public function isValid(): bool
    {
        if ($this->expiresAt === null) {
            return true; // Token bez expirácie
        }

        return $this->expiresAt > new \DateTimeImmutable();
    }

    /**
     * Mám toto oprávnenie?
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->permissions, true);
    }

    /**
     * Vytvor nový token
     */
    public static function create(
        UserId $userId, 
        array $permissions = [], 
        ?\DateTimeImmutable $expiresAt = null
    ): self {
        return new self(
            tokenId: bin2hex(random_bytes(16)),
            userId: $userId,
            issuedAt: new \DateTimeImmutable(),
            expiresAt: $expiresAt,
            permissions: $permissions
        );
    }

    /**
     * Som rovnaký ako iný token?
     */
    public function equals(AuthToken $other): bool
    {
        return $this->tokenId === $other->tokenId;
    }
}
