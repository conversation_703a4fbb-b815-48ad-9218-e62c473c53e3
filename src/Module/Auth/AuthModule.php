<?php

declare(strict_types=1);

namespace App\Module\Auth;

use App\Shared\Domain\Module\ModuleInterface;
use App\Shared\Infrastructure\DI\RegistryInterface;
use App\Module\Auth\Domain\Service\AuthServiceInterface;
use App\Module\Auth\Infrastructure\Service\SessionAuth;
use App\Module\Auth\Infrastructure\Service\NullAuth;

/**
 * AuthModule - pravda o overení
 * 
 * Nezávislá jednotka zodpovedná za autentifikáciu a autorizáciu.
 * Vie kto môže čo, nezávisle od toho kto je kto (to vie UserModule).
 */
final class AuthModule implements ModuleInterface
{
    /**
     * Kto som?
     */
    public function getName(): string
    {
        return 'auth';
    }

    /**
     * Na kom závisím?
     * 
     * Auth závisí na User module - potrebuje vedieť kto je kto,
     * aby mohol rozhodnúť kto môže čo.
     */
    public function getDependencies(): array
    {
        return ['user']; // Potrebujem UserService pre overenie identity
    }

    /**
     * Ako sa registrujem?
     * 
     * Registruje služby pre autentifikáciu a autorizáciu.
     * Používa SessionAuth ako default implementáciu.
     */
    public function register(RegistryInterface $registry): void
    {
        // Hlavná služba - AuthService
        $this->registerAuthService($registry);

        // Middleware pre HTTP autentifikáciu
        $this->registerMiddleware($registry);

        // Pomocné služby
        $this->registerHelperServices($registry);
    }

    /**
     * Registruj AuthService implementácie
     */
    private function registerAuthService(RegistryInterface $registry): void
    {
        // SessionAuth ako hlavná implementácia
        $registry->factory(SessionAuth::class, function () use ($registry) {
            return new SessionAuth(
                $registry->get(\App\Module\User\Domain\Service\UserServiceInterface::class)
            );
        });

        // NullAuth pre testovanie
        $registry->factory(NullAuth::class, function () {
            return new NullAuth(allowAll: true);
        });

        // Bind interface na SessionAuth ako default
        $registry->factory(AuthServiceInterface::class, function () use ($registry) {
            // V produkčnom prostredí použij SessionAuth
            // V testovom prostredí môže byť prepnuté na NullAuth
            $environment = $_ENV['APP_ENV'] ?? 'production';
            
            if ($environment === 'test') {
                return $registry->get(NullAuth::class);
            }
            
            return $registry->get(SessionAuth::class);
        });
    }

    /**
     * Registruj middleware pre HTTP
     */
    private function registerMiddleware(RegistryInterface $registry): void
    {
        // AuthMiddleware - inject user identity do requestu
        $registry->factory('middleware.auth', function () use ($registry) {
            return new \App\Module\Auth\Infrastructure\Middleware\AuthMiddleware(
                $registry->get(AuthServiceInterface::class)
            );
        });

        // RequireAuthMiddleware - vyžaduje overenie
        $registry->factory('middleware.require_auth', function () use ($registry) {
            return new \App\Module\Auth\Infrastructure\Middleware\RequireAuthMiddleware(
                $registry->get(AuthServiceInterface::class)
            );
        });

        // PermissionMiddleware - kontrola oprávnení
        $registry->factory('middleware.permission', function () use ($registry) {
            return new \App\Module\Auth\Infrastructure\Middleware\PermissionMiddleware(
                $registry->get(AuthServiceInterface::class)
            );
        });
    }

    /**
     * Registruj pomocné služby
     */
    private function registerHelperServices(RegistryInterface $registry): void
    {
        // AuthGuard - helper pre kontrolu oprávnení
        $registry->factory('auth.guard', function () use ($registry) {
            return new \App\Module\Auth\Application\Service\AuthGuard(
                $registry->get(AuthServiceInterface::class)
            );
        });

        // LoginAction - HTTP action pre prihlásenie
        $registry->factory('action.auth.login', function () use ($registry) {
            return new \App\Module\Auth\Infrastructure\Http\LoginAction(
                $registry->get(AuthServiceInterface::class),
                $registry->get(\App\Infrastructure\Responder\JsonResponder::class)
            );
        });

        // LogoutAction - HTTP action pre odhlásenie
        $registry->factory('action.auth.logout', function () use ($registry) {
            return new \App\Module\Auth\Infrastructure\Http\LogoutAction(
                $registry->get(AuthServiceInterface::class),
                $registry->get(\App\Infrastructure\Responder\JsonResponder::class)
            );
        });
    }
}
