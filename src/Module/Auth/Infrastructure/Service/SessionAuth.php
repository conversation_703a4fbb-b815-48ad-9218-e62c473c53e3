<?php

declare(strict_types=1);

namespace App\Module\Auth\Infrastructure\Service;

use App\Module\Auth\Domain\Service\AuthServiceInterface;
use App\Module\Auth\Domain\ValueObject\AuthToken;
use App\Module\Auth\Domain\ValueObject\Permission;
use App\Module\User\Domain\Service\UserServiceInterface;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * SessionAuth - spôsob overenia cez PHP session
 * 
 * Implementuje AuthServiceInterface pomocou PHP sessions.
 * Ukladá token do $_SESSION a overuje cez UserService.
 */
final class SessionAuth implements AuthServiceInterface
{
    private const SESSION_TOKEN_KEY = 'auth_token';
    private const SESSION_USER_ID_KEY = 'auth_user_id';
    private const SESSION_PERMISSIONS_KEY = 'auth_permissions';

    public function __construct(
        private UserServiceInterface $userService
    ) {
        // Zabezpe<PERSON> že session je spustená
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
    }

    /**
     * Kto si? - overenie cez UserService
     */
    public function authenticate(string $identity, string $credential): ?AuthToken
    {
        // Nájdi používateľa podľa emailu
        $user = $this->userService->findUserByEmail($identity);
        
        if ($user === null) {
            return null;
        }

        // Over heslo
        if (!password_verify($credential, $user->getPasswordHash())) {
            return null;
        }

        // Vytvor token
        $permissions = $this->getUserPermissions($user->getId());
        $token = AuthToken::create(
            userId: $user->getId(),
            permissions: array_map(fn($p) => $p->toString(), $permissions),
            expiresAt: new \DateTimeImmutable('+24 hours')
        );

        // Ulož do session
        $_SESSION[self::SESSION_TOKEN_KEY] = $token->getTokenId();
        $_SESSION[self::SESSION_USER_ID_KEY] = $user->getId()->toString();
        $_SESSION[self::SESSION_PERMISSIONS_KEY] = $token->getPermissions();

        return $token;
    }

    /**
     * Môžeš? - kontrola oprávnení
     */
    public function authorize(AuthToken $token, Permission $permission): bool
    {
        if (!$this->isTokenValid($token)) {
            return false;
        }

        return $token->hasPermission($permission->toString());
    }

    /**
     * Odíď. - zrušenie session
     */
    public function logout(AuthToken $token): void
    {
        unset($_SESSION[self::SESSION_TOKEN_KEY]);
        unset($_SESSION[self::SESSION_USER_ID_KEY]);
        unset($_SESSION[self::SESSION_PERMISSIONS_KEY]);
        
        // Zruš celú session ak je prázdna
        if (empty($_SESSION)) {
            session_destroy();
        }
    }

    /**
     * Si ešte tu? - kontrola platnosti
     */
    public function isTokenValid(AuthToken $token): bool
    {
        // Kontrola expirácie
        if (!$token->isValid()) {
            return false;
        }

        // Kontrola session
        return isset($_SESSION[self::SESSION_TOKEN_KEY]) 
            && $_SESSION[self::SESSION_TOKEN_KEY] === $token->getTokenId();
    }

    /**
     * Obnov sa. - refresh token
     */
    public function refreshToken(AuthToken $token): ?AuthToken
    {
        if (!$this->isTokenValid($token)) {
            return null;
        }

        // Vytvor nový token s rovnakými oprávneniami
        $newToken = AuthToken::create(
            userId: $token->getUserId(),
            permissions: $token->getPermissions(),
            expiresAt: new \DateTimeImmutable('+24 hours')
        );

        // Aktualizuj session
        $_SESSION[self::SESSION_TOKEN_KEY] = $newToken->getTokenId();

        return $newToken;
    }

    /**
     * Kto si teraz? - aktuálny token
     */
    public function getCurrentToken(): ?AuthToken
    {
        if (!isset($_SESSION[self::SESSION_TOKEN_KEY], $_SESSION[self::SESSION_USER_ID_KEY])) {
            return null;
        }

        try {
            $userId = UserId::fromString($_SESSION[self::SESSION_USER_ID_KEY]);
            $permissions = $_SESSION[self::SESSION_PERMISSIONS_KEY] ?? [];

            $token = new AuthToken(
                tokenId: $_SESSION[self::SESSION_TOKEN_KEY],
                userId: $userId,
                issuedAt: new \DateTimeImmutable(), // Aproximácia
                expiresAt: new \DateTimeImmutable('+24 hours'), // Aproximácia
                permissions: $permissions
            );

            return $this->isTokenValid($token) ? $token : null;
            
        } catch (\Exception) {
            return null;
        }
    }

    /**
     * Aké máš oprávnenia?
     */
    public function getTokenPermissions(AuthToken $token): array
    {
        return array_map(
            fn($permissionString) => Permission::fromString($permissionString),
            $token->getPermissions()
        );
    }

    /**
     * Získaj oprávnenia používateľa (zjednodušené)
     * 
     * @param UserId $userId
     * @return Permission[]
     */
    private function getUserPermissions(UserId $userId): array
    {
        // Zjednodušená logika - všetci majú základné oprávnenia
        // V skutočnosti by sa to načítalo z databázy alebo role systému
        return [
            Permission::userRead(),
            Permission::markCreate(),
            Permission::markRead(),
            Permission::markUpdate(),
        ];
    }
}
