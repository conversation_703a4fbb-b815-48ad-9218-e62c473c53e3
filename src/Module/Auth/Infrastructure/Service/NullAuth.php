<?php

declare(strict_types=1);

namespace App\Module\Auth\Infrastructure\Service;

use App\Module\Auth\Domain\Service\AuthServiceInterface;
use App\Module\Auth\Domain\ValueObject\AuthToken;
use App\Module\Auth\Domain\ValueObject\Permission;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * NullAuth - spôsob pre testovanie bez skutočného overenia
 * 
 * Implementuje AuthServiceInterface pre testovacie účely.
 * Všetko povolí alebo vráti null podľa konfigurácie.
 */
final class NullAuth implements AuthServiceInterface
{
    private ?AuthToken $currentToken = null;
    private bool $allowAll;
    private array $allowedPermissions;

    public function __construct(
        bool $allowAll = true,
        array $allowedPermissions = []
    ) {
        $this->allowAll = $allowAll;
        $this->allowedPermissions = $allowedPermissions;
    }

    /**
     * Kto si? - v<PERSON><PERSON> ú<PERSON>n<PERSON> overenie
     */
    public function authenticate(string $identity, string $credential): ?AuthToken
    {
        if (!$this->allowAll && $identity !== '<EMAIL>') {
            return null;
        }

        $this->currentToken = AuthToken::create(
            userId: UserId::generate(),
            permissions: array_map(fn($p) => $p->toString(), $this->getAllowedPermissions()),
            expiresAt: new \DateTimeImmutable('+1 hour')
        );

        return $this->currentToken;
    }

    /**
     * Môžeš? - povolí všetko alebo len povolené oprávnenia
     */
    public function authorize(AuthToken $token, Permission $permission): bool
    {
        if ($this->allowAll) {
            return true;
        }

        return in_array(
            $permission->toString(),
            array_map(fn($p) => $p->toString(), $this->allowedPermissions),
            true
        );
    }

    /**
     * Odíď. - zruší aktuálny token
     */
    public function logout(AuthToken $token): void
    {
        $this->currentToken = null;
    }

    /**
     * Si ešte tu? - vždy platný ak existuje
     */
    public function isTokenValid(AuthToken $token): bool
    {
        return $this->currentToken !== null && $this->currentToken->equals($token);
    }

    /**
     * Obnov sa. - vytvorí nový token
     */
    public function refreshToken(AuthToken $token): ?AuthToken
    {
        if (!$this->isTokenValid($token)) {
            return null;
        }

        $this->currentToken = AuthToken::create(
            userId: $token->getUserId(),
            permissions: $token->getPermissions(),
            expiresAt: new \DateTimeImmutable('+1 hour')
        );

        return $this->currentToken;
    }

    /**
     * Kto si teraz? - vráti aktuálny token
     */
    public function getCurrentToken(): ?AuthToken
    {
        return $this->currentToken;
    }

    /**
     * Aké máš oprávnenia?
     */
    public function getTokenPermissions(AuthToken $token): array
    {
        return array_map(
            fn($permissionString) => Permission::fromString($permissionString),
            $token->getPermissions()
        );
    }

    /**
     * Nastav aktuálny token (pre testovanie)
     */
    public function setCurrentToken(?AuthToken $token): void
    {
        $this->currentToken = $token;
    }

    /**
     * Nastav povolené oprávnenia (pre testovanie)
     */
    public function setAllowedPermissions(array $permissions): void
    {
        $this->allowedPermissions = $permissions;
        $this->allowAll = false;
    }

    /**
     * Povol všetko (pre testovanie)
     */
    public function allowAll(): void
    {
        $this->allowAll = true;
    }

    /**
     * Zakáž všetko (pre testovanie)
     */
    public function denyAll(): void
    {
        $this->allowAll = false;
        $this->allowedPermissions = [];
    }

    /**
     * Získaj povolené oprávnenia
     */
    private function getAllowedPermissions(): array
    {
        if ($this->allowAll) {
            return [
                Permission::userCreate(),
                Permission::userRead(),
                Permission::userUpdate(),
                Permission::userDelete(),
                Permission::markCreate(),
                Permission::markRead(),
                Permission::markUpdate(),
                Permission::markDelete(),
                Permission::adminAccess(),
            ];
        }

        return $this->allowedPermissions;
    }
}
