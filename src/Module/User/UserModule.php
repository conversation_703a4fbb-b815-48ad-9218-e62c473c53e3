<?php

declare(strict_types=1);

namespace App\Module\User;

use App\Shared\Domain\Module\ModuleInterface;
use App\Shared\Infrastructure\DI\RegistryInterface;
use App\Module\User\Domain\Repository\UserRepositoryInterface;
use App\Module\User\Domain\Service\UserServiceInterface;
use App\Module\User\Application\Service\UserService;
use App\Infrastructure\Persistence\User\DbUserRepository;

/**
 * UserModule - pravda o User module
 * 
 * Nezávislá jednotka zodpovedná za správu používateľov.
 * Komunikuje len cez rozhrania, nie implementácie.
 * Testovateľná v izolácii s mock implementáciami.
 */
final class UserModule implements ModuleInterface
{
    /**
     * Kto som?
     */
    public function getName(): string
    {
        return 'user';
    }

    /**
     * Na kom závisím?
     * 
     * User modul je základný - nez<PERSON><PERSON><PERSON> na iných moduloch.
     * (Neskôr možno bude závisieť na 'auth' module)
     */
    public function getDependencies(): array
    {
        return []; // Nezávislý modul
    }

    /**
     * Ako sa registrujem?
     * 
     * Registruje všetky služby potrebné pre User modul.
     * Používa len abstraktné rozhrania - žiadne konkrétne závislosti.
     */
    public function register(RegistryInterface $registry): void
    {
        // Repository - spôsob uloženia User entít
        $registry->bind(
            UserRepositoryInterface::class,
            DbUserRepository::class
        );

        // Service - orchestrácia User operácií  
        $registry->bind(
            UserServiceInterface::class,
            UserService::class
        );

        // Actions - HTTP rozhranie (voliteľné, môže byť v inom module)
        $this->registerActions($registry);

        // Commands & Queries - CQRS rozhranie (voliteľné)
        $this->registerCommandsAndQueries($registry);
    }

    /**
     * Registruj HTTP actions pre User modul
     */
    private function registerActions(RegistryInterface $registry): void
    {
        // Create User Action
        $registry->factory('action.user.create', function () use ($registry) {
            return new \App\Module\User\Create\Action\UserCreateAction(
                $registry->get(\Psr\Log\LoggerInterface::class),
                $registry->get(\App\Infrastructure\Responder\JsonResponder::class),
                $registry->get(\App\Module\User\Create\Service\UserCreator::class)
            );
        });

        // Read User Action  
        $registry->factory('action.user.read', function () use ($registry) {
            return new \App\Module\User\Read\Action\UserReadPageAction(
                $registry->get(\Slim\Views\PhpRenderer::class),
                $registry->get(\App\Module\User\Read\Service\UserReadFinder::class)
            );
        });

        // List Users Action
        $registry->factory('action.user.list', function () use ($registry) {
            return new \App\Module\User\List\Action\UserListPageAction(
                $registry->get(\Slim\Views\PhpRenderer::class),
                $registry->get(\App\Module\User\List\Service\UserListFinder::class)
            );
        });

        // Update User Action
        $registry->factory('action.user.update', function () use ($registry) {
            return new \App\Module\User\Update\Action\UserUpdateAction(
                $registry->get(\App\Infrastructure\Responder\JsonResponder::class),
                $registry->get(\App\Module\User\Update\Service\UserUpdater::class)
            );
        });

        // Delete User Action
        $registry->factory('action.user.delete', function () use ($registry) {
            return new \App\Module\User\Delete\Action\UserDeleteAction(
                $registry->get(\App\Infrastructure\Responder\JsonResponder::class),
                $registry->get(\App\Module\User\Delete\Service\UserDeleter::class)
            );
        });
    }

    /**
     * Registruj Commands & Queries pre CQRS pattern
     */
    private function registerCommandsAndQueries(RegistryInterface $registry): void
    {
        // User Creator Service
        $registry->autowire(\App\Module\User\Create\Service\UserCreator::class);
        
        // User Read Services
        $registry->autowire(\App\Module\User\Read\Service\UserReadFinder::class);
        $registry->autowire(\App\Module\User\List\Service\UserListFinder::class);
        
        // User Update Service
        $registry->autowire(\App\Module\User\Update\Service\UserUpdater::class);
        
        // User Delete Service  
        $registry->autowire(\App\Module\User\Delete\Service\UserDeleter::class);

        // User List Service (legacy)
        $registry->autowire(\App\Module\User\Read\Service\UserListService::class);
    }
}
