<?php

declare(strict_types=1);

namespace App\Module\User\Domain\Service;

use App\Module\User\Domain\Entity\User;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * UserServiceInterface - pravda o User službách
 * 
 * Definuje čo môžeme robiť s User entitami.
 * Nezávisí na implementácii - len definuje pravdu.
 */
interface UserServiceInterface
{
    /**
     * Vytvor nového používateľa
     * 
     * @param array<string, mixed> $userData Dáta používateľa
     * @return UserId ID vytvoreného používateľa
     */
    public function createUser(array $userData): UserId;

    /**
     * Nájdi používateľa podľa ID
     * 
     * @param UserId $id ID používateľa
     * @return User|null Používateľ alebo null ak neexistuje
     */
    public function findUser(UserId $id): ?User;

    /**
     * Nájdi používateľa podľa emailu
     * 
     * @param string $email Email používateľa
     * @return User|null Používateľ alebo null ak neexistuje
     */
    public function findUserByEmail(string $email): ?User;

    /**
     * Aktualizuj používateľa
     * 
     * @param UserId $id ID používateľa
     * @param array<string, mixed> $userData Nové dáta
     * @return bool True ak bol aktualizovaný
     */
    public function updateUser(UserId $id, array $userData): bool;

    /**
     * Odstráň používateľa
     * 
     * @param UserId $id ID používateľa
     * @return bool True ak bol odstránený
     */
    public function deleteUser(UserId $id): bool;

    /**
     * Získaj zoznam používateľov
     * 
     * @param int $limit Limit počtu používateľov
     * @param int $offset Offset pre stránkovanie
     * @return User[] Zoznam používateľov
     */
    public function listUsers(int $limit = 10, int $offset = 0): array;

    /**
     * Spočítaj používateľov
     * 
     * @return int Počet používateľov
     */
    public function countUsers(): int;
}
