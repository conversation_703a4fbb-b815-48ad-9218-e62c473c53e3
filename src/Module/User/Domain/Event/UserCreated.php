<?php

declare(strict_types=1);

namespace App\Module\User\Domain\Event;

use App\Shared\Domain\Event\DomainEvent;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * UserCreated - udalosť "Používateľ bol vytvorený"
 *
 * <PERSON><PERSON><PERSON><PERSON> svetu, že sa narodila nová identita.
 */
final readonly class UserCreated extends DomainEvent
{
    /**
     * Vytvor udalosť o vytvorení používateľa
     */
    public static function occur(UserId $userId, string $email, string $username): self
    {
        return self::create([
            'userId' => (string)$userId,
            'email' => $email,
            'username' => $username,
        ]);
    }

    /**
     * Kto bol vytvorený?
     */
    public function getUserId(): UserId
    {
        return UserId::fromString($this->getEventData()['userId']);
    }

    /**
     * <PERSON>ký má email?
     */
    public function getEmail(): string
    {
        return $this->getEventData()['email'];
    }

    /**
     * <PERSON><PERSON><PERSON> má používateľské meno?
     */
    public function getUsername(): string
    {
        return $this->getEventData()['username'];
    }
}
