<?php

declare(strict_types=1);

namespace App\Module\User\Application\Service;

use App\Module\User\Domain\Entity\User;
use App\Module\User\Domain\Repository\UserRepositoryInterface;
use App\Module\User\Domain\Service\UserServiceInterface;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * UserService - implementácia UserServiceInterface
 * 
 * Orchestruje User operácie cez repository.
 * Obsahuje business logiku pre prácu s používateľmi.
 */
final readonly class UserService implements UserServiceInterface
{
    public function __construct(
        private UserRepositoryInterface $userRepository
    ) {}

    /**
     * Vytvor nového používateľa
     */
    public function createUser(array $userData): UserId
    {
        // Validácia dát
        $this->validateUserData($userData);

        // Vytvor User entitu
        $userId = UserId::generate();
        $user = new User(
            $userId,
            $userData['email'],
            $userData['username'],
            password_hash($userData['password'], PASSWORD_DEFAULT),
            $userData['firstName'] ?? null,
            $userData['lastName'] ?? null,
            $userData['isActive'] ?? true
        );

        // Ulož do repository
        $this->userRepository->save($user);

        return $userId;
    }

    /**
     * Nájdi používateľa podľa ID
     */
    public function findUser(UserId $id): ?User
    {
        return $this->userRepository->find($id);
    }

    /**
     * Nájdi používateľa podľa emailu
     */
    public function findUserByEmail(string $email): ?User
    {
        return $this->userRepository->findByEmail($email);
    }

    /**
     * Aktualizuj používateľa
     */
    public function updateUser(UserId $id, array $userData): bool
    {
        $user = $this->userRepository->find($id);
        if (!$user) {
            return false;
        }

        // Aktualizuj dáta (tu by bola business logika)
        // Pre jednoduchosť len uložíme znovu
        $this->userRepository->save($user);

        return true;
    }

    /**
     * Odstráň používateľa
     */
    public function deleteUser(UserId $id): bool
    {
        $user = $this->userRepository->find($id);
        if (!$user) {
            return false;
        }

        $this->userRepository->delete($user);
        return true;
    }

    /**
     * Získaj zoznam používateľov
     */
    public function listUsers(int $limit = 10, int $offset = 0): array
    {
        return $this->userRepository->findAll($limit, $offset);
    }

    /**
     * Spočítaj používateľov
     */
    public function countUsers(): int
    {
        return $this->userRepository->count();
    }

    /**
     * Validuj dáta používateľa
     * 
     * @param array<string, mixed> $userData
     * @throws \InvalidArgumentException
     */
    private function validateUserData(array $userData): void
    {
        if (empty($userData['email'])) {
            throw new \InvalidArgumentException('Email je povinný');
        }

        if (empty($userData['username'])) {
            throw new \InvalidArgumentException('Username je povinný');
        }

        if (empty($userData['password'])) {
            throw new \InvalidArgumentException('Password je povinný');
        }

        // Kontrola duplicity emailu
        if ($this->userRepository->findByEmail($userData['email'])) {
            throw new \InvalidArgumentException('Email už existuje');
        }
    }
}
