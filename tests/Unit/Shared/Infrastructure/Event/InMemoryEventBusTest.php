<?php

declare(strict_types=1);

namespace Tests\Unit\Shared\Infrastructure\Event;

use PHPUnit\Framework\TestCase;
use App\Shared\Infrastructure\Event\InMemoryEventBus;
use App\Shared\Domain\Event\DomainEvent;
use App\Module\User\Domain\Event\UserCreated;
use App\Module\User\Domain\ValueObject\UserId;

/**
 * InMemoryEventBusTest - test pravdy o komunikácii
 * 
 * Testuje že EventBus vie prenášať správy medzi modulmi.
 */
final class InMemoryEventBusTest extends TestCase
{
    private InMemoryEventBus $eventBus;

    protected function setUp(): void
    {
        $this->eventBus = new InMemoryEventBus();
    }

    protected function tearDown(): void
    {
        $this->eventBus->clear();
    }

    /**
     * Test že môžeme odoslať udalosť
     */
    public function testCanDispatchEvent(): void
    {
        // Arrange
        $event = UserCreated::occur(
            UserId::generate(),
            '<EMAIL>',
            'testuser'
        );

        // Act
        $this->eventBus->dispatch($event);

        // Assert
        $dispatchedEvents = $this->eventBus->getDispatchedEvents();
        $this->assertCount(1, $dispatchedEvents);
        $this->assertSame($event, $dispatchedEvents[0]);
    }

    /**
     * Test že môžeme sa prihlásiť na odber udalostí
     */
    public function testCanSubscribeToEvents(): void
    {
        // Arrange
        $handlerCalled = false;
        $receivedEvent = null;

        $handler = function (DomainEvent $event) use (&$handlerCalled, &$receivedEvent) {
            $handlerCalled = true;
            $receivedEvent = $event;
        };

        // Act
        $this->eventBus->subscribe(UserCreated::class, $handler);

        // Assert
        $this->assertTrue($this->eventBus->hasSubscribers(UserCreated::class));
        $this->assertSame(1, $this->eventBus->getSubscriberCount(UserCreated::class));
    }

    /**
     * Test že handler sa zavolá pri odoslaní udalosti
     */
    public function testHandlerIsCalledWhenEventIsDispatched(): void
    {
        // Arrange
        $handlerCalled = false;
        $receivedEvent = null;

        $handler = function (DomainEvent $event) use (&$handlerCalled, &$receivedEvent) {
            $handlerCalled = true;
            $receivedEvent = $event;
        };

        $this->eventBus->subscribe(UserCreated::class, $handler);

        $event = UserCreated::occur(
            UserId::generate(),
            '<EMAIL>',
            'testuser'
        );

        // Act
        $this->eventBus->dispatch($event);

        // Assert
        $this->assertTrue($handlerCalled);
        $this->assertSame($event, $receivedEvent);
    }

    /**
     * Test že viacerí handleri sa zavolajú
     */
    public function testMultipleHandlersAreCalled(): void
    {
        // Arrange
        $handler1Called = false;
        $handler2Called = false;

        $handler1 = function () use (&$handler1Called) {
            $handler1Called = true;
        };

        $handler2 = function () use (&$handler2Called) {
            $handler2Called = true;
        };

        $this->eventBus->subscribe(UserCreated::class, $handler1);
        $this->eventBus->subscribe(UserCreated::class, $handler2);

        $event = UserCreated::occur(
            UserId::generate(),
            '<EMAIL>',
            'testuser'
        );

        // Act
        $this->eventBus->dispatch($event);

        // Assert
        $this->assertTrue($handler1Called);
        $this->assertTrue($handler2Called);
        $this->assertSame(2, $this->eventBus->getSubscriberCount(UserCreated::class));
    }

    /**
     * Test že môžeme zrušiť odber
     */
    public function testCanUnsubscribeFromEvents(): void
    {
        // Arrange
        $handler = function () {};
        $this->eventBus->subscribe(UserCreated::class, $handler);

        // Act
        $this->eventBus->unsubscribe(UserCreated::class, $handler);

        // Assert
        $this->assertFalse($this->eventBus->hasSubscribers(UserCreated::class));
        $this->assertSame(0, $this->eventBus->getSubscriberCount(UserCreated::class));
    }

    /**
     * Test že môžeme zrušiť všetkých poslucháčov
     */
    public function testCanUnsubscribeAllHandlers(): void
    {
        // Arrange
        $handler1 = function () {};
        $handler2 = function () {};
        
        $this->eventBus->subscribe(UserCreated::class, $handler1);
        $this->eventBus->subscribe(UserCreated::class, $handler2);

        // Act
        $this->eventBus->unsubscribe(UserCreated::class);

        // Assert
        $this->assertFalse($this->eventBus->hasSubscribers(UserCreated::class));
        $this->assertSame(0, $this->eventBus->getSubscriberCount(UserCreated::class));
    }

    /**
     * Test že môžeme získať posledné udalosti
     */
    public function testCanGetLastEventOfType(): void
    {
        // Arrange
        $event1 = UserCreated::occur(UserId::generate(), '<EMAIL>', 'user1');
        $event2 = UserCreated::occur(UserId::generate(), '<EMAIL>', 'user2');

        // Act
        $this->eventBus->dispatch($event1);
        $this->eventBus->dispatch($event2);

        // Assert
        $lastEvent = $this->eventBus->getLastEventOfType(UserCreated::class);
        $this->assertSame($event2, $lastEvent);
        $this->assertSame(2, $this->eventBus->countEventsOfType(UserCreated::class));
    }

    /**
     * Test že handler chyby neovplyvnia ostatných
     */
    public function testHandlerExceptionDoesNotAffectOthers(): void
    {
        // Arrange
        $handler1Called = false;
        $handler2Called = false;

        $handler1 = function () {
            throw new \Exception('Handler 1 failed');
        };

        $handler2 = function () use (&$handler2Called) {
            $handler2Called = true;
        };

        $this->eventBus->subscribe(UserCreated::class, $handler1);
        $this->eventBus->subscribe(UserCreated::class, $handler2);

        $event = UserCreated::occur(
            UserId::generate(),
            '<EMAIL>',
            'testuser'
        );

        // Act
        $this->eventBus->dispatch($event);

        // Assert - handler2 sa mal zavolať napriek chybe v handler1
        $this->assertTrue($handler2Called);
    }

    /**
     * Test že môžeme vyčistiť históriu
     */
    public function testCanClearHistory(): void
    {
        // Arrange
        $event = UserCreated::occur(
            UserId::generate(),
            '<EMAIL>',
            'testuser'
        );
        $this->eventBus->dispatch($event);

        // Act
        $this->eventBus->clearHistory();

        // Assert
        $this->assertEmpty($this->eventBus->getDispatchedEvents());
    }
}
