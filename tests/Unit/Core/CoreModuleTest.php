<?php

declare(strict_types=1);

namespace Tests\Unit\Core;

use PHPUnit\Framework\TestCase;
use App\Core\CoreModule;
use App\Shared\Infrastructure\DI\MockRegistry;
use App\Shared\Domain\Event\EventBusInterface;
use App\Shared\Infrastructure\Event\InMemoryEventBus;
use App\Shared\Infrastructure\Event\NullEventBus;
use App\Core\Domain\Service\ClockInterface;

/**
 * CoreModuleTest - test pravdy Core modulu
 * 
 * Testuje registráciu základných služieb pre celú aplikáciu.
 * Overuje že Core modul poskytuje základy pre ostatné moduly.
 */
final class CoreModuleTest extends TestCase
{
    private MockRegistry $mockRegistry;
    private CoreModule $coreModule;

    protected function setUp(): void
    {
        $this->mockRegistry = new MockRegistry();
        $this->coreModule = new CoreModule();
    }

    protected function tearDown(): void
    {
        $this->mockRegistry->clear();
    }

    /**
     * Test základných vlastností modulu
     */
    public function testModuleBasicProperties(): void
    {
        // Assert - over základné vlastnosti
        $this->assertSame('core', $this->coreModule->getName());
        $this->assertSame([], $this->coreModule->getDependencies());
    }

    /**
     * Test že Core modul je nezávislý
     */
    public function testModuleIsIndependent(): void
    {
        // Assert - Core musí byť nezávislý
        $dependencies = $this->coreModule->getDependencies();
        
        $this->assertEmpty($dependencies);
        $this->assertIsArray($dependencies);
    }

    /**
     * Test registrácie EventBus služieb
     */
    public function testModuleRegistersEventBus(): void
    {
        // Act - zaregistruj modul
        $this->coreModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované EventBus služby
        $this->assertTrue(
            $this->mockRegistry->has(EventBusInterface::class),
            'EventBusInterface musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has(InMemoryEventBus::class),
            'InMemoryEventBus musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has(NullEventBus::class),
            'NullEventBus musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie Logger služieb
     */
    public function testModuleRegistersLogger(): void
    {
        // Act - zaregistruj modul
        $this->coreModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované Logger služby
        $this->assertTrue(
            $this->mockRegistry->has(\Psr\Log\LoggerInterface::class),
            'LoggerInterface musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has('logger.null'),
            'Null logger musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie Settings služieb
     */
    public function testModuleRegistersSettings(): void
    {
        // Act - zaregistruj modul
        $this->coreModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované Settings služby
        $this->assertTrue(
            $this->mockRegistry->has('settings'),
            'Settings musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie Clock služieb
     */
    public function testModuleRegistersClock(): void
    {
        // Act - zaregistruj modul
        $this->coreModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované Clock služby
        $this->assertTrue(
            $this->mockRegistry->has(ClockInterface::class),
            'ClockInterface musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has('clock.fixed'),
            'Fixed clock musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie utility služieb
     */
    public function testModuleRegistersUtilities(): void
    {
        // Act - zaregistruj modul
        $this->coreModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované utility služby
        $expectedUtilities = [
            'uuid.generator',
            'hash.service',
            'validation.service',
        ];

        foreach ($expectedUtilities as $utility) {
            $this->assertTrue(
                $this->mockRegistry->has($utility),
                "Utility '{$utility}' musí byť zaregistrovaná"
            );
        }
    }

    /**
     * Test že registrácia prebehne bez chýb
     */
    public function testRegistrationDoesNotThrow(): void
    {
        // Act & Assert - registrácia nesmie hodiť výnimku
        $this->expectNotToPerformAssertions();
        $this->coreModule->register($this->mockRegistry);
    }

    /**
     * Test výberu EventBus implementácie podľa prostredia
     */
    public function testEventBusImplementationSelection(): void
    {
        // Arrange - nastav test prostredie
        $_ENV['APP_ENV'] = 'test';

        // Act - zaregistruj modul
        $this->coreModule->register($this->mockRegistry);

        // Assert - v test prostredí by mal byť použitý NullEventBus
        $eventBus = $this->mockRegistry->get(EventBusInterface::class);
        $this->assertInstanceOf(NullEventBus::class, $eventBus);

        // Cleanup
        unset($_ENV['APP_ENV']);
    }

    /**
     * Test zoznamu všetkých zaregistrovaných služieb
     */
    public function testGetAllRegisteredServices(): void
    {
        // Act - zaregistruj modul
        $this->coreModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované všetky očakávané služby
        $registeredServices = $this->mockRegistry->getRegisteredServices();
        
        $this->assertGreaterThan(
            10,
            count($registeredServices),
            'Core modul musí zaregistrovať viac ako 10 služieb'
        );

        // Over že obsahuje kľúčové služby
        $this->assertContains(EventBusInterface::class, $registeredServices);
        $this->assertContains(\Psr\Log\LoggerInterface::class, $registeredServices);
        $this->assertContains(ClockInterface::class, $registeredServices);
        $this->assertContains('settings', $registeredServices);
    }

    /**
     * Test že Core modul je správne navrhnutý
     */
    public function testModuleDesignPrinciples(): void
    {
        // Assert - over princípy návrhu
        
        // 1. Má jasný názov
        $this->assertNotEmpty($this->coreModule->getName());
        
        // 2. Je nezávislý (žiadne závislosti)
        $this->assertEmpty($this->coreModule->getDependencies());
        
        // 3. Implementuje ModuleInterface
        $this->assertInstanceOf(\App\Shared\Domain\Module\ModuleInterface::class, $this->coreModule);
    }

    /**
     * Test že Core modul má správnu zodpovednosť
     */
    public function testModuleResponsibility(): void
    {
        // Assert - Core modul poskytuje základné služby
        $this->assertSame('core', $this->coreModule->getName());
        
        // Je nezávislý - je to základ pre ostatné
        $this->assertEmpty($this->coreModule->getDependencies());
    }
}
