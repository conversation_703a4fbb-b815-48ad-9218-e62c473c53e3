<?php

declare(strict_types=1);

namespace Tests\Unit\Module\User;

use PHPUnit\Framework\TestCase;
use App\Module\User\UserModule;
use App\Shared\Infrastructure\DI\MockRegistry;
use App\Module\User\Domain\Repository\UserRepositoryInterface;
use App\Module\User\Domain\Service\UserServiceInterface;

/**
 * UserModuleTest - test pravdy User modulu
 *
 * Testuje registráciu služieb v izolácii bez zá<PERSON>lostí na frameworku.
 * Používa MockRegistry pre simuláciu DI kontajnera.
 */
final class UserModuleTest extends TestCase
{
    private MockRegistry $mockRegistry;
    private UserModule $userModule;

    protected function setUp(): void
    {
        $this->mockRegistry = new MockRegistry();
        $this->userModule = new UserModule();
    }

    protected function tearDown(): void
    {
        $this->mockRegistry->clear();
    }

    /**
     * Test základných vlastností modulu
     */
    public function testModuleBasicProperties(): void
    {
        // Assert - over základné vlastnosti
        $this->assertSame('user', $this->userModule->getName());
        $this->assertSame([], $this->userModule->getDependencies());
    }

    /**
     * Test registrácie základných služieb
     */
    public function testModuleRegistersBasicServices(): void
    {
        // Act - zaregistruj modul
        $this->userModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované základné služby
        $this->assertTrue(
            $this->mockRegistry->has(UserRepositoryInterface::class),
            'UserRepositoryInterface musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has(UserServiceInterface::class),
            'UserServiceInterface musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie HTTP actions
     */
    public function testModuleRegistersActions(): void
    {
        // Act - zaregistruj modul
        $this->userModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované actions
        $expectedActions = [
            'action.user.create',
            'action.user.read',
            'action.user.list',
            'action.user.update',
            'action.user.delete',
        ];

        foreach ($expectedActions as $action) {
            $this->assertTrue(
                $this->mockRegistry->has($action),
                "Action '{$action}' musí byť zaregistrovaná"
            );
        }
    }

    /**
     * Test registrácie CQRS služieb
     */
    public function testModuleRegistersCQRSServices(): void
    {
        // Act - zaregistruj modul
        $this->userModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované CQRS služby
        $expectedServices = [
            \App\Module\User\Create\Service\UserCreator::class,
            \App\Module\User\Read\Service\UserReadFinder::class,
            \App\Module\User\List\Service\UserListFinder::class,
            \App\Module\User\Update\Service\UserUpdater::class,
            \App\Module\User\Delete\Service\UserDeleter::class,
            \App\Module\User\Read\Service\UserListService::class,
        ];

        foreach ($expectedServices as $service) {
            $this->assertTrue(
                $this->mockRegistry->has($service),
                "Service '{$service}' musí byť zaregistrovaná"
            );
        }
    }

    /**
     * Test že modul je nezávislý
     */
    public function testModuleIsIndependent(): void
    {
        // Assert - over že modul nemá závislosti
        $this->assertEmpty(
            $this->userModule->getDependencies(),
            'User modul musí byť nezávislý'
        );
    }

    /**
     * Test že registrácia prebehne bez chýb
     */
    public function testRegistrationDoesNotThrow(): void
    {
        // Act & Assert - registrácia nesmie hodiť výnimku
        $this->expectNotToPerformAssertions();
        $this->userModule->register($this->mockRegistry);
    }

    /**
     * Test že môžeme získať zaregistrované služby
     */
    public function testCanGetRegisteredServices(): void
    {
        // Arrange - priprav mock implementácie pre závislosti
        $mockUserRepository = $this->createMock(UserRepositoryInterface::class);
        $this->mockRegistry->setMock(UserRepositoryInterface::class, $mockUserRepository);

        // Mock pre Cake\Database\Connection (potrebné pre DbUserRepository)
        $mockConnection = $this->createMock(\Cake\Database\Connection::class);
        $this->mockRegistry->setMock(\Cake\Database\Connection::class, $mockConnection);

        // Act - zaregistruj modul
        $this->userModule->register($this->mockRegistry);

        // Assert - over že môžeme získať služby
        $this->assertInstanceOf(
            UserRepositoryInterface::class,
            $this->mockRegistry->get(UserRepositoryInterface::class)
        );
    }

    /**
     * Test zoznamu všetkých zaregistrovaných služieb
     */
    public function testGetAllRegisteredServices(): void
    {
        // Act - zaregistruj modul
        $this->userModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované všetky očakávané služby
        $registeredServices = $this->mockRegistry->getRegisteredServices();

        $this->assertGreaterThan(
            10,
            count($registeredServices),
            'User modul musí zaregistrovať viac ako 10 služieb'
        );

        // Over že obsahuje kľúčové služby
        $this->assertContains(UserRepositoryInterface::class, $registeredServices);
        $this->assertContains(UserServiceInterface::class, $registeredServices);
    }
}
