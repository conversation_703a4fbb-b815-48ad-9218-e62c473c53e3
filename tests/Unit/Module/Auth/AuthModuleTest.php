<?php

declare(strict_types=1);

namespace Tests\Unit\Module\Auth;

use PHPUnit\Framework\TestCase;
use App\Module\Auth\AuthModule;
use App\Shared\Infrastructure\DI\MockRegistry;
use App\Module\Auth\Domain\Service\AuthServiceInterface;
use App\Module\Auth\Infrastructure\Service\SessionAuth;
use App\Module\Auth\Infrastructure\Service\NullAuth;
use App\Module\User\Domain\Service\UserServiceInterface;

/**
 * AuthModuleTest - test pravdy Auth modulu
 * 
 * Testuje registráciu služieb pre autentifikáciu a autorizáciu.
 * Overuje že Auth modul vie kto môže čo.
 */
final class AuthModuleTest extends TestCase
{
    private MockRegistry $mockRegistry;
    private AuthModule $authModule;

    protected function setUp(): void
    {
        $this->mockRegistry = new MockRegistry();
        $this->authModule = new AuthModule();
        
        // Priprav mock UserService (závislosť Auth modulu)
        $mockUserService = $this->createMock(UserServiceInterface::class);
        $this->mockRegistry->setMock(UserServiceInterface::class, $mockUserService);
    }

    protected function tearDown(): void
    {
        $this->mockRegistry->clear();
    }

    /**
     * Test základných vlastností modulu
     */
    public function testModuleBasicProperties(): void
    {
        // Assert - over základné vlastnosti
        $this->assertSame('auth', $this->authModule->getName());
        $this->assertSame(['user'], $this->authModule->getDependencies());
    }

    /**
     * Test že Auth modul závisí na User module
     */
    public function testModuleDependsOnUser(): void
    {
        // Assert - Auth musí závisieť na User
        $dependencies = $this->authModule->getDependencies();
        
        $this->assertContains('user', $dependencies);
        $this->assertCount(1, $dependencies);
    }

    /**
     * Test registrácie AuthService implementácií
     */
    public function testModuleRegistersAuthServices(): void
    {
        // Act - zaregistruj modul
        $this->authModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované auth služby
        $this->assertTrue(
            $this->mockRegistry->has(AuthServiceInterface::class),
            'AuthServiceInterface musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has(SessionAuth::class),
            'SessionAuth musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has(NullAuth::class),
            'NullAuth musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie middleware
     */
    public function testModuleRegistersMiddleware(): void
    {
        // Act - zaregistruj modul
        $this->authModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované middleware
        $expectedMiddleware = [
            'middleware.auth',
            'middleware.require_auth',
            'middleware.permission',
        ];

        foreach ($expectedMiddleware as $middleware) {
            $this->assertTrue(
                $this->mockRegistry->has($middleware),
                "Middleware '{$middleware}' musí byť zaregistrovaný"
            );
        }
    }

    /**
     * Test registrácie HTTP actions
     */
    public function testModuleRegistersActions(): void
    {
        // Act - zaregistruj modul
        $this->authModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované actions
        $expectedActions = [
            'action.auth.login',
            'action.auth.logout',
        ];

        foreach ($expectedActions as $action) {
            $this->assertTrue(
                $this->mockRegistry->has($action),
                "Action '{$action}' musí byť zaregistrovaná"
            );
        }
    }

    /**
     * Test registrácie pomocných služieb
     */
    public function testModuleRegistersHelperServices(): void
    {
        // Act - zaregistruj modul
        $this->authModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované pomocné služby
        $this->assertTrue(
            $this->mockRegistry->has('auth.guard'),
            'AuthGuard musí byť zaregistrovaný'
        );
    }

    /**
     * Test že registrácia prebehne bez chýb
     */
    public function testRegistrationDoesNotThrow(): void
    {
        // Act & Assert - registrácia nesmie hodiť výnimku
        $this->expectNotToPerformAssertions();
        $this->authModule->register($this->mockRegistry);
    }

    /**
     * Test že môžeme získať AuthService
     */
    public function testCanGetAuthService(): void
    {
        // Act - zaregistruj modul
        $this->authModule->register($this->mockRegistry);

        // Assert - over že môžeme získať AuthService
        $authService = $this->mockRegistry->get(AuthServiceInterface::class);
        $this->assertInstanceOf(AuthServiceInterface::class, $authService);
    }

    /**
     * Test výberu implementácie podľa prostredia
     */
    public function testEnvironmentBasedImplementationSelection(): void
    {
        // Arrange - nastav test prostredie
        $_ENV['APP_ENV'] = 'test';

        // Act - zaregistruj modul
        $this->authModule->register($this->mockRegistry);

        // Assert - v test prostredí by mal byť použitý NullAuth
        $authService = $this->mockRegistry->get(AuthServiceInterface::class);
        $this->assertInstanceOf(NullAuth::class, $authService);

        // Cleanup
        unset($_ENV['APP_ENV']);
    }

    /**
     * Test zoznamu všetkých zaregistrovaných služieb
     */
    public function testGetAllRegisteredServices(): void
    {
        // Act - zaregistruj modul
        $this->authModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované všetky očakávané služby
        $registeredServices = $this->mockRegistry->getRegisteredServices();
        
        $this->assertGreaterThan(
            8,
            count($registeredServices),
            'Auth modul musí zaregistrovať viac ako 8 služieb'
        );

        // Over že obsahuje kľúčové služby
        $this->assertContains(AuthServiceInterface::class, $registeredServices);
        $this->assertContains(SessionAuth::class, $registeredServices);
        $this->assertContains(NullAuth::class, $registeredServices);
    }

    /**
     * Test že Auth modul je správne navrhnutý
     */
    public function testModuleDesignPrinciples(): void
    {
        // Assert - over princípy návrhu
        
        // 1. Má jasný názov
        $this->assertNotEmpty($this->authModule->getName());
        
        // 2. Deklaruje závislosti
        $this->assertIsArray($this->authModule->getDependencies());
        
        // 3. Závisí len na User module
        $this->assertContains('user', $this->authModule->getDependencies());
        
        // 4. Implementuje ModuleInterface
        $this->assertInstanceOf(\App\Shared\Domain\Module\ModuleInterface::class, $this->authModule);
    }
}
