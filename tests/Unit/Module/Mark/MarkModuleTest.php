<?php

declare(strict_types=1);

namespace Tests\Unit\Module\Mark;

use PHPUnit\Framework\TestCase;
use App\Module\Mark\MarkModule;
use App\Shared\Infrastructure\DI\MockRegistry;
use App\Module\Mark\Domain\Repository\MarkRepositoryInterface;
use App\Module\Mark\Domain\Service\MarkServiceInterface;
use App\Infrastructure\Persistence\Mark\DbMarkRepository;
use App\Module\Mark\Application\Service\MarkService;

/**
 * MarkModuleTest - test pravdy Mark modulu
 *
 * Testuje registráciu služieb pre správu označení.
 * Overuje že Mark modul vie čo je označené.
 */
final class MarkModuleTest extends TestCase
{
    private MockRegistry $mockRegistry;
    private MarkModule $markModule;

    protected function setUp(): void
    {
        $this->mockRegistry = new MockRegistry();
        $this->markModule = new MarkModule();

        // Priprav mock databázové pripojenie (závislosť Mark modulu)
        $mockConnection = $this->createMock(\Cake\Database\Connection::class);
        $this->mockRegistry->setMock('db.connection.mark', $mockConnection);
    }

    protected function tearDown(): void
    {
        $this->mockRegistry->clear();
    }

    /**
     * Test základných vlastností modulu
     */
    public function testModuleBasicProperties(): void
    {
        // Assert - over základné vlastnosti
        $this->assertSame('mark', $this->markModule->getName());
        $this->assertSame(['user'], $this->markModule->getDependencies());
    }

    /**
     * Test že Mark modul závisí na User module
     */
    public function testModuleDependsOnUser(): void
    {
        // Assert - Mark musí závisieť na User
        $dependencies = $this->markModule->getDependencies();

        $this->assertContains('user', $dependencies);
        $this->assertCount(1, $dependencies);
    }

    /**
     * Test registrácie repository služieb
     */
    public function testModuleRegistersRepository(): void
    {
        // Act - zaregistruj modul
        $this->markModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované repository služby
        $this->assertTrue(
            $this->mockRegistry->has(MarkRepositoryInterface::class),
            'MarkRepositoryInterface musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has(DbMarkRepository::class),
            'DbMarkRepository musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie hlavných služieb
     */
    public function testModuleRegistersMainServices(): void
    {
        // Act - zaregistruj modul
        $this->markModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované hlavné služby
        $this->assertTrue(
            $this->mockRegistry->has(MarkServiceInterface::class),
            'MarkServiceInterface musí byť zaregistrovaný'
        );

        $this->assertTrue(
            $this->mockRegistry->has(MarkService::class),
            'MarkService musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie CQRS handlers
     */
    public function testModuleRegistersCQRSHandlers(): void
    {
        // Act - zaregistruj modul
        $this->markModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované CQRS handlers
        $expectedHandlers = [
            \App\Module\Mark\Application\Command\CreateMarkHandler::class,
            \App\Module\Mark\Application\Query\GetMarksHandler::class,
            \App\Module\Mark\Application\Command\CreateMarkCommand::class,
            \App\Module\Mark\Application\Query\GetMarksQuery::class,
        ];

        foreach ($expectedHandlers as $handler) {
            $this->assertTrue(
                $this->mockRegistry->has($handler),
                "Handler '{$handler}' musí byť zaregistrovaný"
            );
        }
    }

    /**
     * Test registrácie HTTP controllers
     */
    public function testModuleRegistersControllers(): void
    {
        // Act - zaregistruj modul
        $this->markModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované controllers
        $this->assertTrue(
            $this->mockRegistry->has(\App\Module\Mark\Infrastructure\Http\Controller\MarkController::class),
            'MarkController musí byť zaregistrovaný'
        );
    }

    /**
     * Test registrácie API actions
     */
    public function testModuleRegistersApiActions(): void
    {
        // Act - zaregistruj modul
        $this->markModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované API actions
        $expectedActions = [
            'action.mark.create',
            'action.mark.list',
            'action.mark.update',
            'action.mark.delete',
        ];

        foreach ($expectedActions as $action) {
            $this->assertTrue(
                $this->mockRegistry->has($action),
                "Action '{$action}' musí byť zaregistrovaná"
            );
        }
    }

    /**
     * Test že registrácia prebehne bez chýb
     */
    public function testRegistrationDoesNotThrow(): void
    {
        // Act & Assert - registrácia nesmie hodiť výnimku
        $this->expectNotToPerformAssertions();
        $this->markModule->register($this->mockRegistry);
    }

    /**
     * Test že môžeme získať MarkService
     */
    public function testCanGetMarkService(): void
    {
        // Arrange - priprav mock implementácie pre závislosti
        $mockMarkRepository = $this->createMock(MarkRepositoryInterface::class);
        $this->mockRegistry->setMock(MarkRepositoryInterface::class, $mockMarkRepository);

        // Act - zaregistruj modul
        $this->markModule->register($this->mockRegistry);

        // Assert - over že môžeme získať MarkService
        $markService = $this->mockRegistry->get(MarkServiceInterface::class);
        $this->assertInstanceOf(MarkServiceInterface::class, $markService);
    }

    /**
     * Test zoznamu všetkých zaregistrovaných služieb
     */
    public function testGetAllRegisteredServices(): void
    {
        // Act - zaregistruj modul
        $this->markModule->register($this->mockRegistry);

        // Assert - over že sú zaregistrované všetky očakávané služby
        $registeredServices = $this->mockRegistry->getRegisteredServices();

        $this->assertGreaterThan(
            10,
            count($registeredServices),
            'Mark modul musí zaregistrovať viac ako 10 služieb'
        );

        // Over že obsahuje kľúčové služby
        $this->assertContains(MarkRepositoryInterface::class, $registeredServices);
        $this->assertContains(MarkServiceInterface::class, $registeredServices);
        $this->assertContains(DbMarkRepository::class, $registeredServices);
        $this->assertContains(MarkService::class, $registeredServices);
    }

    /**
     * Test že Mark modul je správne navrhnutý
     */
    public function testModuleDesignPrinciples(): void
    {
        // Assert - over princípy návrhu

        // 1. Má jasný názov
        $this->assertNotEmpty($this->markModule->getName());

        // 2. Deklaruje závislosti
        $this->assertIsArray($this->markModule->getDependencies());

        // 3. Závisí len na User module
        $this->assertContains('user', $this->markModule->getDependencies());

        // 4. Implementuje ModuleInterface
        $this->assertInstanceOf(\App\Shared\Domain\Module\ModuleInterface::class, $this->markModule);
    }

    /**
     * Test že Mark modul má správnu zodpovednosť
     */
    public function testModuleResponsibility(): void
    {
        // Assert - Mark modul je zodpovedný za označenia
        $this->assertSame('mark', $this->markModule->getName());

        // Závisí na User (potrebuje vedieť kto označuje)
        $this->assertContains('user', $this->markModule->getDependencies());

        // Nezávisí na Auth (to je zodpovednosť vyšších vrstiev)
        $this->assertNotContains('auth', $this->markModule->getDependencies());
    }
}
